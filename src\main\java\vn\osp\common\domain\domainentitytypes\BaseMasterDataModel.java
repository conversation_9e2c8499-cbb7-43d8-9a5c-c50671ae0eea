package vn.osp.common.domain.domainentitytypes;

import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Class base chứa các field chung nhất của danh mục,
 * khi tạo mới 1 danh mục cần extends từ class này.
 */
@MappedSuperclass
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public abstract class BaseMasterDataModel
        extends BaseDomainEntityUuid
        implements HasOrder,
        HasEnabled,
        HasCode,
        SoftDeleteUuid,
        HasCreatedByUuid,
        HasModifiedByUuid,
        HasTenantId {

    /**
     * Tên
     */
    @Column(nullable = false, length = 500)
    private String title = "";

    /**
     * Mã
     */
    @Column(nullable = false, unique = true)
    private String code;

    /**
     * Thứ tự hiển thị
     */
    @Column(nullable = false, name = "display_order")
    private int order = 0;

    /**
     * Trạng thái sử dụng hay không
     */
    @Column(nullable = false)
    private boolean enabled = false;

    /**
     * Mô tả
     */
    @Column(length = 1000)
    private String description;

    /**
     * Trạng thái đã xóa hay chưa
     */
    @Column(nullable = false)
    private boolean isDeleted;

    /**
     * Thời gian xóa
     */
    @Column()
    private LocalDateTime deletedAt;

    /**
     * Id người xóa
     */
    @Column()
    private UUID deletedBy;

    /**
     * Thời gian tạo
     */
    @Column(nullable = false)
    private LocalDateTime createdAt;

    /**
     * Id người tạo
     */
    @Column()
    private UUID createdBy;

    /**
     * Thời gian sửa
     */
    @Column()
    private LocalDateTime modifiedAt;

    /**
     * Id người sửa
     */
    @Column()
    private UUID modifiedBy;

    /**
     * Mã tenantId (hiện tại chưa hoàn thiện), phục vụ triển khai ứng dụng mô hình SaaS.
     */
    @Column()
    private UUID tenantId;
}