package vn.osp.common.infrastructure.helpers;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import vn.osp.common.domain.helpers.JsonHelper;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Tiện ích hỗ trợ thao tác với HTTP request/response trong Spring Boot.
 */
public final class HttpHelper {

    private HttpHelper() {
        // Ngăn tạo instance
    }

    // ==========================
    // Headers
    // ==========================

    /**
     * Lấy giá trị header từ HTTP request.
     *
     * @param request      HTTP servlet request
     * @param name         tên header cần lấy
     * @param defaultValue giá trị mặc định nếu header không tồn tại
     * @return giá trị header hoặc defaultValue nếu không tìm thấy
     */
    public static String getHeader(HttpServletRequest request, String name, String defaultValue) {
        String value = request.getHeader(name);
        return (value != null) ? value : defaultValue;
    }

    /**
     * Trả về tất cả headers từ HTTP request dưới dạng Map.
     *
     * @param request HTTP servlet request
     * @return Map chứa tất cả headers với key là tên header và value là giá trị header
     */
    public static Map<String, String> getHeaders(HttpServletRequest request) {
        return Collections.list(request.getHeaderNames())
                .stream()
                .collect(Collectors.toMap(h -> h, request::getHeader));
    }

    // ==========================
    // URL / Domain
    // ==========================

    /**
     * Lấy domain (base URL) từ request.
     *
     * @param request HTTP servlet request
     * @return base URL bao gồm scheme, server name và port (nếu cần)
     */
    public static String getBaseUrl(HttpServletRequest request) {
        String scheme = request.getScheme();
        String serverName = request.getServerName();
        int port = request.getServerPort();

        if ((scheme.equals("http") && port == 80) || (scheme.equals("https") && port == 443)) {
            return scheme + "://" + serverName;
        }
        return scheme + "://" + serverName + ":" + port;
    }

    /**
     * Lấy full URL (bao gồm query string).
     *
     * @param request HTTP servlet request
     * @return full URL bao gồm cả query string nếu có
     */
    public static String getFullUrl(HttpServletRequest request) {
        return request.getRequestURL().toString() +
                (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    }

    /**
     * Lấy subdomain từ HTTP request.
     * Ví dụ: api.example.com -> "api", www.example.com -> "www"
     *
     * @param request HTTP servlet request
     * @return subdomain hoặc null nếu không có subdomain hoặc là IP address/localhost
     */
    public static String getSubdomain(HttpServletRequest request) {
        String serverName = request.getServerName();
        if (serverName == null || serverName.isEmpty()) {
            return null;
        }

        // Bỏ qua IP address
        if (serverName.matches("\\d+\\.\\d+\\.\\d+\\.\\d+")) {
            return null;
        }

        // Bỏ qua localhost
        if ("localhost".equalsIgnoreCase(serverName)) {
            return null;
        }

        String[] parts = serverName.split("\\.");
        
        // Cần ít nhất 3 phần để có subdomain (subdomain.domain.tld)
        if (parts.length < 3) {
            return null;
        }

        // Trả về phần đầu tiên là subdomain
        return parts[0];
    }

    // ==========================
    // Client info
    // ==========================

    /**
     * Lấy địa chỉ IP thực của client, có xét đến proxy và load balancer.
     * Kiểm tra header X-Forwarded-For trước khi fallback về RemoteAddr.
     *
     * @param request HTTP servlet request
     * @return địa chỉ IP của client
     */
    public static String getClientIp(HttpServletRequest request) {
        String ip = Optional.ofNullable(request.getHeader("X-Forwarded-For"))
                .orElse(request.getRemoteAddr());
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim(); // lấy IP đầu tiên trong chuỗi
        }
        return ip;
    }

    /**
     * Lấy User-Agent từ HTTP request.
     *
     * @param request HTTP servlet request
     * @return giá trị User-Agent header, có thể là null nếu không tồn tại
     */
    public static String getUserAgent(HttpServletRequest request) {
        return request.getHeader("User-Agent");
    }

    // ==========================
    // Cookies
    // ==========================

    /**
     * Lấy cookie theo tên từ HTTP request.
     *
     * @param request HTTP servlet request
     * @param name    tên cookie cần lấy
     * @return giá trị cookie hoặc null nếu không tìm thấy
     */
    public static String getCookie(HttpServletRequest request, String name) {
        if (request.getCookies() == null) return null;
        return Arrays.stream(request.getCookies())
                .filter(c -> c.getName().equals(name))
                .map(Cookie::getValue)
                .findFirst()
                .orElse(null);
    }

    /**
     * Thêm cookie vào HTTP response.
     *
     * @param response HTTP servlet response
     * @param name     tên cookie
     * @param value    giá trị cookie
     * @param maxAge   thời gian sống của cookie (giây)
     */
    public static void addCookie(HttpServletResponse response, String name, String value, int maxAge) {
        Cookie cookie = new Cookie(name, value);
        cookie.setPath("/");
        cookie.setMaxAge(maxAge);
        cookie.setHttpOnly(true);
        response.addCookie(cookie);
    }

    /**
     * Xóa cookie khỏi HTTP response bằng cách đặt maxAge = 0.
     *
     * @param response HTTP servlet response
     * @param name     tên cookie cần xóa
     */
    public static void deleteCookie(HttpServletResponse response, String name) {
        Cookie cookie = new Cookie(name, null);
        cookie.setPath("/");
        cookie.setMaxAge(0);
        response.addCookie(cookie);
    }

    // ==========================
    // Request body
    // ==========================

    /**
     * Đọc body từ HttpServletRequest.
     * Lưu ý: chỉ dùng khi chắc chắn input stream chưa bị đọc trước đó.
     *
     * @param request HTTP servlet request
     * @return nội dung body dưới dạng String
     * @throws IOException nếu có lỗi khi đọc input stream
     */
    public static String getBody(HttpServletRequest request) throws IOException {
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = request.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        }
        return sb.toString();
    }

    // ==========================
    // Response helpers
    // ==========================

    /**
     * Ghi JSON object ra HTTP response.
     *
     * @param response HTTP servlet response
     * @param obj      object cần serialize thành JSON
     * @throws IOException nếu có lỗi khi ghi response
     */
    public static void writeJson(HttpServletResponse response, Object obj) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        JsonHelper.mapper.writeValue(response.getWriter(), obj);
    }

    /**
     * Ghi text/plain ra HTTP response.
     *
     * @param response HTTP servlet response
     * @param text     nội dung text cần ghi
     * @throws IOException nếu có lỗi khi ghi response
     */
    public static void writeText(HttpServletResponse response, String text) throws IOException {
        response.setContentType("text/plain;charset=UTF-8");
        response.getWriter().write(text);
    }
}
