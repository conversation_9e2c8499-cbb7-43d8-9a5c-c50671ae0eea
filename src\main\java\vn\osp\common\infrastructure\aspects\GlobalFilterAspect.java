package vn.osp.common.infrastructure.aspects;

import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.hibernate.Session;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import vn.osp.common.infrastructure.filters.GlobalFilterManager;

/**
 * Aspect tự động enable global filters cho các repository methods và service methods.
 * <PERSON><PERSON><PERSON> bảo filters được áp dụng cho tất cả database operations.
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
@Order(1) // Chạy trước các aspect khác
public class GlobalFilterAspect {

    private final GlobalFilterManager filterManager;

    /**
     * Intercept tất cả repository methods để enable global filters.
     */
    @Around("execution(* org.springframework.data.repository.Repository+.*(..))")
    public Object enableFiltersForRepositoryMethods(ProceedingJoinPoint joinPoint) throws Throwable {
        return executeWithGlobalFilters(joinPoint, "Repository method");
    }

    /**
     * Intercept các service methods có annotation @Transactional để enable global filters.
     */
    @Around("@annotation(org.springframework.transaction.annotation.Transactional)")
    public Object enableFiltersForTransactionalMethods(ProceedingJoinPoint joinPoint) throws Throwable {
        return executeWithGlobalFilters(joinPoint, "Transactional method");
    }

    /**
     * Intercept các methods có annotation @EnableGlobalFilters.
     */
    @Around("@annotation(vn.osp.common.infrastructure.annotations.EnableGlobalFilters)")
    public Object enableFiltersForAnnotatedMethods(ProceedingJoinPoint joinPoint) throws Throwable {
        return executeWithGlobalFilters(joinPoint, "Annotated method");
    }

    /**
     * Intercept các methods có annotation @DisableGlobalFilters.
     */
    @Around("@annotation(vn.osp.common.infrastructure.annotations.DisableGlobalFilters)")
    public Object disableFiltersForAnnotatedMethods(ProceedingJoinPoint joinPoint) throws Throwable {
        return executeWithoutGlobalFilters(joinPoint, "Disable filters method");
    }

    /**
     * Thực thi method với global filters được enable.
     */
    private Object executeWithGlobalFilters(ProceedingJoinPoint joinPoint, String methodType) throws Throwable {
        try {
            // Lấy EntityManager từ context nếu có
            EntityManager entityManager = getCurrentEntityManager();
            if (entityManager != null) {
                Session session = entityManager.unwrap(Session.class);
                filterManager.enableGlobalFilters(session);
                log.debug("Enabled global filters for {}: {}", methodType, joinPoint.getSignature().getName());
            }
        } catch (Exception e) {
            log.debug("Could not enable global filters for {}: {}", methodType, e.getMessage());
        }

        return joinPoint.proceed();
    }

    /**
     * Thực thi method với global filters được disable.
     */
    private Object executeWithoutGlobalFilters(ProceedingJoinPoint joinPoint, String methodType) throws Throwable {
        try {
            // Lấy EntityManager từ context nếu có
            EntityManager entityManager = getCurrentEntityManager();
            if (entityManager != null) {
                Session session = entityManager.unwrap(Session.class);
                return filterManager.executeWithoutFilters(session, () -> {
                    try {
                        log.debug("Disabled global filters for {}: {}", methodType, joinPoint.getSignature().getName());
                        return joinPoint.proceed();
                    } catch (Throwable throwable) {
                        throw new RuntimeException(throwable);
                    }
                });
            }
        } catch (Exception e) {
            log.debug("Could not disable global filters for {}: {}", methodType, e.getMessage());
        }

        return joinPoint.proceed();
    }

    /**
     * Lấy EntityManager hiện tại từ Spring context.
     * Có thể cần customize tùy theo cách setup EntityManager trong dự án.
     */
    private EntityManager getCurrentEntityManager() {
        try {
            // Cách 1: Sử dụng @PersistenceContext injection (nếu có)
            // return entityManager;

            // Cách 2: Lấy từ Spring ApplicationContext
            // ApplicationContext context = ApplicationContextProvider.getApplicationContext();
            // return context.getBean(EntityManager.class);

            // Cách 3: Lấy từ EntityManagerFactory
            // return entityManagerFactory.createEntityManager();

            // Tạm thời return null, sẽ được customize trong implementation cụ thể
            return null;
        } catch (Exception e) {
            log.debug("Could not get current EntityManager: {}", e.getMessage());
            return null;
        }
    }
}
