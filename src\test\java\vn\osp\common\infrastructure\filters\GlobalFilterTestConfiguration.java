package vn.osp.common.infrastructure.filters;

import org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.test.context.ActiveProfiles;

@Configuration
@ActiveProfiles("test")
@EnableJpaRepositories(basePackages = "vn.osp.common.infrastructure.repositories")
public class GlobalFilterTestConfiguration {

    @Bean
    public HibernatePropertiesCustomizer hibernatePropertiesCustomizer() {
        return (hibernateProperties) -> {
            System.out.println("🔧 Customizing Hibernate properties for global filters");
            
            // Sử dụng SessionFactoryBuilderFactory thay vì Integrator
            hibernateProperties.put("hibernate.session_factory_name_is_jndi", false);
            hibernateProperties.put("hibernate.session_factory_builder_factory", 
                          "vn.osp.common.infrastructure.filters.TestSessionFactoryBuilderFactory");
            
            System.out.println("✅ SessionFactoryBuilderFactory configured");
        };
    }
}
