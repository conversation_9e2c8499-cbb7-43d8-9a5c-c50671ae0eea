# Global Filters Implementation

## Tổng quan

Đã triển khai thành công hệ thống global filter tự động cho soft delete, multi-tenant và enabled status mà không cần annotate từng entity. Hệ thống này đảm bảo data isolation và security một cách tự động và transparent.

## Kiến trúc

### Core Components

1. **GlobalFilterManager** - Quản lý enable/disable filters
2. **GlobalFilterInterceptor** - Hibernate interceptor tự động enable filters
3. **GlobalFilterConfiguration** - Configuration và filter definitions
4. **GlobalFilterAspect** - AOP aspect cho repository/service methods
5. **EntityManagerFilterHelper** - Helper utilities cho EntityManager
6. **BaseFilteredRepository** - Base repository với filtering tự động
7. **BaseFilteredService** - Base service với filtering utilities

### Annotations

- **@EnableGlobalFilters** - Explicitly enable filters
- **@DisableGlobalFilters** - Explicitly disable filters (cần cẩn thận)

## Cá<PERSON> thức hoạt động

### 1. Automatic Filter Application

```mermaid
graph TD
    A[Entity implements interfaces] --> B[SoftDeleteUuid, HasTenantId, HasEnabled]
    B --> C[GlobalFilterInterceptor detects]
    C --> D[Auto-enable appropriate filters]
    D --> E[Repository/Service operations filtered]
```

### 2. Filter Hierarchy

```
GlobalFilterManager
├── SoftDeleteFilter (isDeleted = false)
├── TenantFilter (tenantId = :currentTenant)
└── EnabledFilter (enabled = true)
```

### 3. Integration Points

- **Session Level**: GlobalFilterInterceptor
- **Repository Level**: BaseFilteredRepository + Aspect
- **Service Level**: BaseFilteredService + Aspect
- **Manual Control**: EntityManagerFilterHelper

## Key Features

### ✅ Automatic Detection
- Tự động phát hiện entities cần filtering dựa trên interfaces
- Không cần annotate `@Filter` trên entities
- Transparent cho developers

### ✅ Multiple Filter Types
- **Soft Delete**: Ẩn records đã bị xóa mềm
- **Multi-Tenant**: Chỉ hiển thị data của tenant hiện tại
- **Enabled Status**: Chỉ hiển thị records đang active

### ✅ Flexible Control
- Enable/disable toàn bộ hoặc từng filter cụ thể
- Annotation-based control (@EnableGlobalFilters, @DisableGlobalFilters)
- Programmatic control via helper methods

### ✅ Security & Audit
- Require explicit reason khi disable filters
- Permission checks cho sensitive operations
- Comprehensive logging

### ✅ Performance Optimized
- Filters áp dụng ở database level
- Minimal overhead
- Efficient query generation

## Usage Examples

### Entity Definition
```java
@Entity
public class MyEntity extends BaseDomainEntityUuid 
        implements SoftDeleteUuid, HasTenantId, HasEnabled {
    // Filters tự động áp dụng - không cần @Filter annotations
}
```

### Repository Usage
```java
@Repository
public class MyRepositoryImpl extends BaseFilteredRepository<MyEntity, UUID> {
    
    public List<MyEntity> findActive() {
        return findAll(); // Tự động filtered
    }
    
    public List<MyEntity> findAllIncludingDeleted() {
        return findAllWithoutFilters(); // Bypass filters
    }
}
```

### Service Usage
```java
@Service
public class MyService extends BaseFilteredService {
    
    public List<MyEntity> getActiveRecords() {
        return executeWithFilters(() -> repository.findAll());
    }
    
    @DisableGlobalFilters(reason = "Admin report")
    public List<MyEntity> getAdminReport() {
        return repository.findAll(); // All data
    }
}
```

## Benefits

### 🔒 Security by Default
- Data isolation tự động
- Prevent accidental data leakage
- Tenant separation đảm bảo

### 🚀 Developer Experience
- Không cần remember annotate entities
- Consistent behavior across application
- Easy to use utilities

### 🔧 Maintainability
- Centralized filter logic
- Easy to modify filter conditions
- Clear separation of concerns

### 📊 Observability
- Comprehensive logging
- Filter state tracking
- Performance monitoring

## Migration Guide

### From Manual @Filter Annotations

**Before:**
```java
@Entity
@FilterDef(name = "softDelete", defaultCondition = "deleted = false")
@Filter(name = "softDelete")
public class MyEntity {
    // Manual filter management
}
```

**After:**
```java
@Entity
public class MyEntity implements SoftDeleteUuid {
    // Automatic filter management
}
```

### Repository Updates

**Before:**
```java
@Repository
public class MyRepository {
    
    @Query("SELECT e FROM MyEntity e WHERE e.deleted = false")
    public List<MyEntity> findActive() {
        // Manual filtering in queries
    }
}
```

**After:**
```java
@Repository
public class MyRepository extends BaseFilteredRepository<MyEntity, UUID> {
    
    public List<MyEntity> findActive() {
        return findAll(); // Automatic filtering
    }
}
```

## Configuration

### Required Dependencies
- Spring Boot 3.5.5
- Hibernate 6+
- Spring Data JPA
- Spring AOP

### Application Properties
```yaml
spring:
  jpa:
    properties:
      hibernate:
        session_factory.interceptor: vn.osp.common.infrastructure.filters.GlobalFilterInterceptor

logging:
  level:
    vn.osp.common.infrastructure.filters: DEBUG
```

## Testing

Comprehensive test suite bao gồm:
- Unit tests cho từng component
- Integration tests cho end-to-end scenarios
- Performance tests cho filter overhead
- Security tests cho data isolation

## Future Enhancements

### Planned Features
- [ ] Dynamic filter conditions
- [ ] Custom filter definitions
- [ ] Filter caching optimization
- [ ] Metrics và monitoring dashboard
- [ ] Filter audit trail

### Extensibility Points
- Custom filter implementations
- Additional filter types
- Integration với security frameworks
- Custom permission checks

## Conclusion

Hệ thống global filters đã được triển khai thành công với các đặc điểm:

- **Automatic**: Không cần manual configuration
- **Secure**: Data isolation by default
- **Flexible**: Multiple control mechanisms
- **Performant**: Database-level filtering
- **Maintainable**: Clean architecture design

Developers có thể sử dụng hệ thống này một cách transparent mà không cần lo lắng về data security và isolation.
