package vn.osp.common.infrastructure.filters;

import jakarta.persistence.EntityManager;
import org.hibernate.Session;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import vn.osp.common.domain.entities.BasicEntity;
import vn.osp.common.domain.entities.ExampleEntity;
import vn.osp.common.infrastructure.repositories.ExampleEntityRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

@DataJpaTest
@ActiveProfiles("test")
@ContextConfiguration(classes = {
        SimpleGlobalFilterTest.TestConfig.class,
})
class SimpleGlobalFilterTest {

    @SpringBootConfiguration
    @EnableAutoConfiguration
    @EntityScan("vn.osp.common.domain.entities")
    @EnableJpaRepositories("vn.osp.common.infrastructure.repositories")
    static class TestConfig {
    }

    @Autowired
    private TestEntityManager testEntityManager;

    @Autowired
    private EntityManager entityManager;

    @Autowired
    private ExampleEntityRepository repository;



    private UUID tenant1Id;
    private UUID tenant2Id;
    private UUID user1Id;

    private ExampleEntity entity1; // tenant1, enabled, not deleted
    private ExampleEntity entity2; // tenant1, disabled, not deleted
    private ExampleEntity entity3; // tenant1, enabled, deleted
    private ExampleEntity entity4; // tenant2, enabled, not deleted
    private ExampleEntity entity5; // no tenant, enabled, not deleted

    @BeforeEach
    void setUp() {
        // Initialize IDs
        tenant1Id = UUID.randomUUID();
        tenant2Id = UUID.randomUUID();
        user1Id = UUID.randomUUID();

        // Create test entities
        entity1 = createEntity("ENTITY_001", "Entity 1", tenant1Id, true, false);
        entity2 = createEntity("ENTITY_002", "Entity 2", tenant1Id, false, false);
        entity3 = createEntity("ENTITY_003", "Entity 3", tenant1Id, true, true);
        entity4 = createEntity("ENTITY_004", "Entity 4", tenant2Id, true, false);
        entity5 = createEntity("ENTITY_005", "Entity 5", null, true, false);

        // Save entities
        testEntityManager.persistAndFlush(entity1);
        testEntityManager.persistAndFlush(entity2);
        testEntityManager.persistAndFlush(entity3);
        testEntityManager.persistAndFlush(entity4);
        testEntityManager.persistAndFlush(entity5);

        // Clear persistence context
        testEntityManager.clear();
    }

    private ExampleEntity createEntity(String code, String title, UUID tenantId, boolean enabled, boolean deleted) {
        ExampleEntity entity = new ExampleEntity();
        entity.setCode(code);
        entity.setTitle(title);
        entity.setTenantId(tenantId);
        entity.setEnabled(enabled);
        entity.setDeleted(deleted);
        entity.setCreatedAt(LocalDateTime.now());
        entity.setCreatedBy(user1Id);

        if (deleted) {
            entity.setDeletedAt(LocalDateTime.now());
            entity.setDeletedBy(user1Id);
        }

        return entity;
    }

    @Test
    void testBasicEntityCreationAndRetrieval() {
        // Given: Entities are created and persisted

        // When: Query all entities using JPQL (no filters applied)
        List<ExampleEntity> entities = entityManager
                .createQuery("SELECT e FROM ExampleEntity e", ExampleEntity.class)
                .getResultList();

        // Then: Should return all 5 entities
        assertThat(entities).hasSize(5);
        assertThat(entities).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("ENTITY_001", "ENTITY_002", "ENTITY_003", "ENTITY_004", "ENTITY_005");
    }

    @Test
    void testHibernateSoftDeleteFilter() {
        // Given: Entities are created
        Session session = entityManager.unwrap(Session.class);

        // When: Apply soft delete filter using Hibernate
        session.enableFilter("softDeleteFilter").setParameter("isDeleted", false);

        List<ExampleEntity> entities = entityManager
                .createQuery("SELECT e FROM ExampleEntity e", ExampleEntity.class)
                .getResultList();

        // Then: Should exclude deleted entity (entity3)
        assertThat(entities).hasSize(4);
        assertThat(entities).extracting(ExampleEntity::getCode)
                .doesNotContain("ENTITY_003");
        assertThat(entities).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("ENTITY_001", "ENTITY_002", "ENTITY_004", "ENTITY_005");
    }

    @Test
    void testHibernateTenantFilter() {
        // Given: Entities are created
        Session session = entityManager.unwrap(Session.class);

        // When: Apply tenant filter using Hibernate for tenant1
        session.enableFilter("tenantFilter").setParameter("tenantId", tenant1Id);

        List<ExampleEntity> entities = entityManager
                .createQuery("SELECT e FROM ExampleEntity e", ExampleEntity.class)
                .getResultList();

        // Then: Should return tenant1 entities and null tenant entities
        assertThat(entities).hasSize(4);
        assertThat(entities).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("ENTITY_001", "ENTITY_002", "ENTITY_003", "ENTITY_005");
    }

    @Test
    void testHibernateEnabledFilter() {
        // Given: Entities are created
        Session session = entityManager.unwrap(Session.class);

        // When: Apply enabled filter using Hibernate
        session.enableFilter("enabledFilter").setParameter("enabled", true);

        List<ExampleEntity> entities = entityManager
                .createQuery("SELECT e FROM ExampleEntity e", ExampleEntity.class)
                .getResultList();

        // Then: Should exclude disabled entity (entity2)
        assertThat(entities).hasSize(4);
        assertThat(entities).extracting(ExampleEntity::getCode)
                .doesNotContain("ENTITY_002");
        assertThat(entities).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("ENTITY_001", "ENTITY_003", "ENTITY_004", "ENTITY_005");
    }

    @Test
    void testHibernateCombinedFilters() {
        // Given: Entities are created
        Session session = entityManager.unwrap(Session.class);

        // When: Apply all Hibernate filters for tenant1
        session.enableFilter("softDeleteFilter").setParameter("isDeleted", false);
        session.enableFilter("tenantFilter").setParameter("tenantId", tenant1Id);
        session.enableFilter("enabledFilter").setParameter("enabled", true);

        List<ExampleEntity> entities = entityManager
                .createQuery("SELECT e FROM ExampleEntity e", ExampleEntity.class)
                .getResultList();

        // Then: Should return only entity1 and entity5 (tenant1 or null, enabled, not deleted)
        assertThat(entities).hasSize(2);
        assertThat(entities).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("ENTITY_001", "ENTITY_005");
    }

    @Test
    void testBasicEntityNotAffectedByHibernateFilters() {
        // Test that BasicEntity (không implement marker interfaces) không bị Hibernate filters ảnh hưởng
        Session session = entityManager.unwrap(Session.class);

        // Tạo test data cho BasicEntity
        BasicEntity basic1 = new BasicEntity(UUID.randomUUID(), "Basic Entity 1");
        BasicEntity basic2 = new BasicEntity(UUID.randomUUID(), "Basic Entity 2");
        BasicEntity basic3 = new BasicEntity(UUID.randomUUID(), "Basic Entity 3");

        testEntityManager.persistAndFlush(basic1);
        testEntityManager.persistAndFlush(basic2);
        testEntityManager.persistAndFlush(basic3);

        // Enable Hibernate filters (sẽ không ảnh hưởng BasicEntity)
        session.enableFilter("softDeleteFilter").setParameter("isDeleted", false);
        session.enableFilter("tenantFilter").setParameter("tenantId", tenant1Id);
        session.enableFilter("enabledFilter").setParameter("enabled", true);

        List<BasicEntity> results = entityManager
                .createQuery("SELECT b FROM BasicEntity b", BasicEntity.class)
                .getResultList();

        // Tất cả BasicEntity phải được trả về vì không có filters áp dụng
        assertThat(results).hasSize(3);
        assertThat(results).extracting(BasicEntity::getName)
                .containsExactlyInAnyOrder("Basic Entity 1", "Basic Entity 2", "Basic Entity 3");
    }

    // ============== Repository Query Filter Tests ==============

    @Test
    void testRepositoryStyleQueriesWithoutFilters() {
        // Simulate repository findByCode method
        Optional<ExampleEntity> result = entityManager.createQuery(
                "SELECT e FROM ExampleEntity e WHERE e.code = :code", ExampleEntity.class)
                .setParameter("code", "ENTITY_003")
                .getResultStream().findFirst();

        // Then: Should find deleted entity
        assertThat(result).isPresent();
        assertThat(result.get().getCode()).isEqualTo("ENTITY_003");
        assertThat(result.get().isDeleted()).isTrue();
    }

    @Test
    void testRepositoryStyleQueriesWithSoftDeleteFilter() {
        // Given: Apply soft delete filter
        Session session = entityManager.unwrap(Session.class);
        session.enableFilter("softDeleteFilter").setParameter("isDeleted", false);

        // When: Simulate repository findByCode for deleted entity
        Optional<ExampleEntity> result = entityManager.createQuery(
                "SELECT e FROM ExampleEntity e WHERE e.code = :code", ExampleEntity.class)
                .setParameter("code", "ENTITY_003")
                .getResultStream().findFirst();

        // Then: Should not find deleted entity
        assertThat(result).isEmpty();

        // When: Simulate repository findByCode for non-deleted entity
        Optional<ExampleEntity> result2 = entityManager.createQuery(
                "SELECT e FROM ExampleEntity e WHERE e.code = :code", ExampleEntity.class)
                .setParameter("code", "ENTITY_001")
                .getResultStream().findFirst();

        // Then: Should find non-deleted entity
        assertThat(result2).isPresent();
        assertThat(result2.get().getCode()).isEqualTo("ENTITY_001");
        assertThat(result2.get().isDeleted()).isFalse();
    }

    @Test
    void testRepositoryStyleFindAllEnabledWithoutFilters() {
        // Simulate repository findAllEnabled method
        List<ExampleEntity> result = entityManager.createQuery(
                "SELECT e FROM ExampleEntity e WHERE e.enabled = true", ExampleEntity.class)
                .getResultList();

        // Then: Should return all enabled entities (including deleted one)
        assertThat(result).hasSize(4); // entity1, entity3 (deleted but enabled), entity4, entity5
        assertThat(result).extracting(ExampleEntity::getCode)
                .contains("ENTITY_001", "ENTITY_003", "ENTITY_004", "ENTITY_005");
    }

    @Test
    void testRepositoryStyleFindAllEnabledWithSoftDeleteFilter() {
        // Given: Apply soft delete filter
        Session session = entityManager.unwrap(Session.class);
        session.enableFilter("softDeleteFilter").setParameter("isDeleted", false);

        // When: Simulate repository findAllEnabled method
        List<ExampleEntity> result = entityManager.createQuery(
                "SELECT e FROM ExampleEntity e WHERE e.enabled = true", ExampleEntity.class)
                .getResultList();

        // Then: Should exclude deleted entity
        assertThat(result).hasSize(3); // entity1, entity4, entity5 (excluding deleted entity3)
        assertThat(result).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("ENTITY_001", "ENTITY_004", "ENTITY_005");
    }

    @Test
    void testRepositoryStyleFindByTenantIdWithoutFilters() {
        // Simulate repository findByTenantId method
        List<ExampleEntity> result = entityManager.createQuery(
                "SELECT e FROM ExampleEntity e WHERE e.tenantId = :tenantId", ExampleEntity.class)
                .setParameter("tenantId", tenant1Id)
                .getResultList();

        // Then: Should return all tenant1 entities (including deleted and disabled)
        assertThat(result).hasSize(3); // entity1, entity2, entity3
        assertThat(result).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("ENTITY_001", "ENTITY_002", "ENTITY_003");
    }

    @Test
    void testRepositoryStyleFindByTenantIdWithFilters() {
        // Given: Apply multiple filters
        Session session = entityManager.unwrap(Session.class);
        session.enableFilter("softDeleteFilter").setParameter("isDeleted", false);
        session.enableFilter("enabledFilter").setParameter("enabled", true);

        // When: Simulate repository findByTenantId method
        List<ExampleEntity> result = entityManager.createQuery(
                "SELECT e FROM ExampleEntity e WHERE e.tenantId = :tenantId", ExampleEntity.class)
                .setParameter("tenantId", tenant1Id)
                .getResultList();

        // Then: Should return only entity1 (tenant1, enabled, not deleted)
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getCode()).isEqualTo("ENTITY_001");
        assertThat(result.get(0).isEnabled()).isTrue();
        assertThat(result.get(0).isDeleted()).isFalse();
    }

    @Test
    void testRepositoryStyleFindAllIncludingDeletedWithTenantFilter() {
        // Given: Apply tenant filter only
        Session session = entityManager.unwrap(Session.class);
        session.enableFilter("tenantFilter").setParameter("tenantId", tenant1Id);

        // When: Simulate repository findAllIncludingDeleted method
        List<ExampleEntity> result = entityManager.createQuery(
                "SELECT e FROM ExampleEntity e", ExampleEntity.class)
                .getResultList();

        // Then: Should return only tenant1 entities and null tenant entities
        assertThat(result).hasSize(4); // entity1, entity2, entity3 (tenant1) + entity5 (null tenant)
        assertThat(result).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("ENTITY_001", "ENTITY_002", "ENTITY_003", "ENTITY_005");

        // Verify no tenant2 entities
        assertThat(result).noneMatch(e -> tenant2Id.equals(e.getTenantId()));
    }

    @Test
    void testRepositoryStyleFindAllWithCombinedFilters() {
        // Given: Apply all filters for tenant1
        Session session = entityManager.unwrap(Session.class);
        session.enableFilter("softDeleteFilter").setParameter("isDeleted", false);
        session.enableFilter("tenantFilter").setParameter("tenantId", tenant1Id);
        session.enableFilter("enabledFilter").setParameter("enabled", true);

        // When: Simulate repository findAllIncludingDeleted method
        List<ExampleEntity> result = entityManager.createQuery(
                "SELECT e FROM ExampleEntity e", ExampleEntity.class)
                .getResultList();

        // Then: Should return only entity1 and entity5 (tenant1 or null, enabled, not deleted)
        assertThat(result).hasSize(2);
        assertThat(result).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("ENTITY_001", "ENTITY_005");

        // Verify properties
        result.forEach(entity -> {
            assertThat(entity.isEnabled()).isTrue();
            assertThat(entity.isDeleted()).isFalse();
            assertThat(entity.getTenantId()).satisfiesAnyOf(
                    tenantId -> assertThat(tenantId).isEqualTo(tenant1Id),
                    tenantId -> assertThat(tenantId).isNull()
            );
        });
    }

    @Test
    void testGlobalFiltersWorkWithAnyJpqlQuery() {
        // Given: Apply filters
        Session session = entityManager.unwrap(Session.class);
        session.enableFilter("softDeleteFilter").setParameter("isDeleted", false);
        session.enableFilter("enabledFilter").setParameter("enabled", true);

        // When: Use any JPQL query (simulating any repository method)
        
        // Count query
        Long count = entityManager.createQuery(
                "SELECT COUNT(e) FROM ExampleEntity e", Long.class)
                .getSingleResult();

        // Find all query
        List<ExampleEntity> allEntities = entityManager.createQuery(
                "SELECT e FROM ExampleEntity e", ExampleEntity.class)
                .getResultList();

        // Custom query with WHERE clause
        List<ExampleEntity> customQuery = entityManager.createQuery(
                "SELECT e FROM ExampleEntity e WHERE e.title LIKE :pattern", ExampleEntity.class)
                .setParameter("pattern", "%Entity%")
                .getResultList();

        // Then: Filters should be applied to ALL queries automatically
        assertThat(count).isEqualTo(3L); // entity1, entity4, entity5 (enabled, not deleted)
        assertThat(allEntities).hasSize(3);
        assertThat(customQuery).hasSize(3);

        allEntities.forEach(entity -> {
            assertThat(entity.isEnabled()).isTrue();
            assertThat(entity.isDeleted()).isFalse();
        });

        customQuery.forEach(entity -> {
            assertThat(entity.isEnabled()).isTrue();
            assertThat(entity.isDeleted()).isFalse();
        });
    }

    // ============== Repository Interface Tests ==============

    @Test
    void testRepositoryFindByCodeWithoutFilters() {
        // When: Use repository method without filters
        Optional<ExampleEntity> result = repository.findByCode("ENTITY_003");

        // Then: Should find deleted entity
        assertThat(result).isPresent();
        assertThat(result.get().getCode()).isEqualTo("ENTITY_003");
        assertThat(result.get().isDeleted()).isTrue();
    }

    @Test
    void testRepositoryFindByCodeWithSoftDeleteFilter() {
        // Given: Apply soft delete filter
        Session session = entityManager.unwrap(Session.class);
        session.enableFilter("softDeleteFilter").setParameter("isDeleted", false);

        // When: Use repository method to find deleted entity
        Optional<ExampleEntity> result = repository.findByCode("ENTITY_003");

        // Then: Should not find deleted entity
        assertThat(result).isEmpty();

        // When: Use repository method to find non-deleted entity
        Optional<ExampleEntity> result2 = repository.findByCode("ENTITY_001");

        // Then: Should find non-deleted entity
        assertThat(result2).isPresent();
        assertThat(result2.get().getCode()).isEqualTo("ENTITY_001");
        assertThat(result2.get().isDeleted()).isFalse();
    }

    @Test
    void testRepositoryFindAllEnabledWithoutFilters() {
        // When: Use repository method without filters
        List<ExampleEntity> result = repository.findAllEnabled();

        // Then: Should return all enabled entities (including deleted one)
        assertThat(result).hasSize(4); // entity1, entity3 (deleted but enabled), entity4, entity5
        assertThat(result).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("ENTITY_001", "ENTITY_003", "ENTITY_004", "ENTITY_005");
    }

    @Test
    void testRepositoryFindAllEnabledWithSoftDeleteFilter() {
        // Given: Apply soft delete filter
        Session session = entityManager.unwrap(Session.class);
        session.enableFilter("softDeleteFilter").setParameter("isDeleted", false);

        // When: Use repository method
        List<ExampleEntity> result = repository.findAllEnabled();

        // Then: Should exclude deleted entity
        assertThat(result).hasSize(3); // entity1, entity4, entity5 (excluding deleted entity3)
        assertThat(result).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("ENTITY_001", "ENTITY_004", "ENTITY_005");
        
        // Verify all returned entities are not deleted
        result.forEach(entity -> assertThat(entity.isDeleted()).isFalse());
    }

    @Test
    void testRepositoryFindByTenantIdWithFilters() {
        // Given: Apply multiple filters
        Session session = entityManager.unwrap(Session.class);
        session.enableFilter("softDeleteFilter").setParameter("isDeleted", false);
        session.enableFilter("enabledFilter").setParameter("enabled", true);

        // When: Use repository method to find by tenant
        List<ExampleEntity> result = repository.findByTenantId(tenant1Id);

        // Then: Should return only entity1 (tenant1, enabled, not deleted)
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getCode()).isEqualTo("ENTITY_001");
        assertThat(result.get(0).isEnabled()).isTrue();
        assertThat(result.get(0).isDeleted()).isFalse();
        assertThat(result.get(0).getTenantId()).isEqualTo(tenant1Id);
    }

    @Test
    void testRepositoryFindAllIncludingDeletedWithTenantFilter() {
        // Given: Apply tenant filter only
        Session session = entityManager.unwrap(Session.class);
        session.enableFilter("tenantFilter").setParameter("tenantId", tenant1Id);

        // When: Use repository method
        List<ExampleEntity> result = repository.findAllIncludingDeleted();

        // Then: Should return only tenant1 entities and null tenant entities
        assertThat(result).hasSize(4); // entity1, entity2, entity3 (tenant1) + entity5 (null tenant)
        assertThat(result).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("ENTITY_001", "ENTITY_002", "ENTITY_003", "ENTITY_005");

        // Verify no tenant2 entities
        assertThat(result).noneMatch(e -> tenant2Id.equals(e.getTenantId()));
    }

    @Test
    void testRepositoryStandardJpaMethodsWithFilters() {
        // Given: Apply filters
        Session session = entityManager.unwrap(Session.class);
        session.enableFilter("softDeleteFilter").setParameter("isDeleted", false);
        session.enableFilter("enabledFilter").setParameter("enabled", true);

        // When: Use standard JPA repository methods
        List<ExampleEntity> allEntities = repository.findAll();
        long count = repository.count();

        // Then: Filters should be applied to standard JPA methods too
        assertThat(allEntities).hasSize(3); // entity1, entity4, entity5 (enabled, not deleted)
        assertThat(count).isEqualTo(3);

        assertThat(allEntities).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("ENTITY_001", "ENTITY_004", "ENTITY_005");

        allEntities.forEach(entity -> {
            assertThat(entity.isEnabled()).isTrue();
            assertThat(entity.isDeleted()).isFalse();
        });
    }

    @Test
    void testRepositoryAllMethodsWithCombinedFilters() {
        // Given: Apply all filters for tenant1
        Session session = entityManager.unwrap(Session.class);
        session.enableFilter("softDeleteFilter").setParameter("isDeleted", false);
        session.enableFilter("tenantFilter").setParameter("tenantId", tenant1Id);
        session.enableFilter("enabledFilter").setParameter("enabled", true);

        // When: Use all repository methods
        Optional<ExampleEntity> byCode = repository.findByCode("ENTITY_001");
        List<ExampleEntity> allEnabled = repository.findAllEnabled();
        List<ExampleEntity> byTenant = repository.findByTenantId(tenant1Id);
        List<ExampleEntity> allIncludingDeleted = repository.findAllIncludingDeleted();
        List<ExampleEntity> standardFindAll = repository.findAll();

        // Then: All repository methods should respect global filters
        assertThat(byCode).isPresent();
        assertThat(byCode.get().getCode()).isEqualTo("ENTITY_001");

        // All methods should return only entity1 and entity5 (tenant1 or null, enabled, not deleted)
        assertThat(allEnabled).hasSize(2);
        assertThat(allEnabled).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("ENTITY_001", "ENTITY_005");

        assertThat(byTenant).hasSize(1); // only entity1 (tenant1)
        assertThat(byTenant.get(0).getCode()).isEqualTo("ENTITY_001");

        assertThat(allIncludingDeleted).hasSize(2); // entity1, entity5
        assertThat(allIncludingDeleted).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("ENTITY_001", "ENTITY_005");

        assertThat(standardFindAll).hasSize(2); // entity1, entity5
        assertThat(standardFindAll).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("ENTITY_001", "ENTITY_005");

        System.out.println("✅ All repository methods work perfectly with global filters!");
    }
}
