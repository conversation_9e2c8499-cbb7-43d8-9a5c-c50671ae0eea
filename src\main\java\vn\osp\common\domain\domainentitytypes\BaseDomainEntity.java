package vn.osp.common.domain.domainentitytypes;

import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.Version;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * Class base, tất cả các model đều cần phải kế thừa từ class này
 * để tận dụng lại các field có sẵn.
 *
 * @param <TKey> Kiểu dữ liệu của khóa chính.
 */
@MappedSuperclass
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public abstract class BaseDomainEntity<TKey extends Serializable> implements Serializable {

    protected BaseDomainEntity(TKey id) {
        this.id = id;
    }

    /**
     * <PERSON><PERSON><PERSON><PERSON> ch<PERSON> của bảng (luôn luôn là Id, thường là kiểu UUID).
     */
    @Id
    @GeneratedValue
    private TKey id;

    /**
     * Trường phục vụ kiểm soát cạnh tranh (optimistic locking).
     */
    @Version
    private int rowVersion;
}