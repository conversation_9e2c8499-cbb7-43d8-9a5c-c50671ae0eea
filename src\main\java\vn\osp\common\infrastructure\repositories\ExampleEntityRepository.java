package vn.osp.common.infrastructure.repositories;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.osp.common.domain.entities.ExampleEntity;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface ExampleEntityRepository extends JpaRepository<ExampleEntity, UUID> {
    @Query("SELECT e FROM ExampleEntity e WHERE e.code = :code")
    Optional<ExampleEntity> findByCode(@Param("code") String code);

    @Query("SELECT e FROM ExampleEntity e WHERE e.enabled = true")
    List<ExampleEntity> findAllEnabled();

    @Query("SELECT e FROM ExampleEntity e WHERE e.tenantId = :tenantId")
    List<ExampleEntity> findByTenantId(@Param("tenantId") UUID tenantId);

    @Query("SELECT e FROM ExampleEntity e")
    List<ExampleEntity> findAllIncludingDeleted();
}
