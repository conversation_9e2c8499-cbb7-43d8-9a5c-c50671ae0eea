package vn.osp.common.infrastructure.services;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import vn.osp.common.infrastructure.annotations.DisableGlobalFilters;
import vn.osp.common.infrastructure.annotations.EnableGlobalFilters;
import vn.osp.common.infrastructure.filters.EntityManagerFilterHelper;

import java.util.function.Supplier;

/**
 * Base service class tự động áp dụng global filters.
 * Cung cấp các utility methods để làm việc với filters.
 */
@Slf4j
public abstract class BaseFilteredService {

    @PersistenceContext
    protected EntityManager entityManager;

    @Autowired
    protected EntityManagerFilterHelper filterHelper;

    /**
     * Thực thi một operation với tất cả global filters được enable.
     */
    @EnableGlobalFilters(reason = "Ensure data isolation and security")
    @Transactional(readOnly = true)
    public <T> T executeWithFilters(Supplier<T> operation) {
        filterHelper.enableGlobalFilters(entityManager);
        return operation.get();
    }

    /**
     * Thực thi một operation với tất cả global filters được disable.
     * Cần cẩn thận khi sử dụng method này.
     */
    @DisableGlobalFilters(reason = "Administrative operation requiring access to all data")
    @Transactional(readOnly = true)
    public <T> T executeWithoutFilters(Supplier<T> operation) {
        return filterHelper.executeWithoutFilters(entityManager, operation);
    }

    /**
     * Thực thi một operation chỉ với soft delete filter.
     */
    @Transactional(readOnly = true)
    public <T> T executeWithSoftDeleteFilterOnly(Supplier<T> operation) {
        return executeWithCustomFilters(() -> {
            filterHelper.disableGlobalFilters(entityManager);
            filterHelper.enableSoftDeleteFilter(entityManager);
            return operation.get();
        });
    }

    /**
     * Thực thi một operation chỉ với tenant filter.
     */
    @Transactional(readOnly = true)
    public <T> T executeWithTenantFilterOnly(Supplier<T> operation) {
        return executeWithCustomFilters(() -> {
            filterHelper.disableGlobalFilters(entityManager);
            filterHelper.enableTenantFilter(entityManager);
            return operation.get();
        });
    }

    /**
     * Thực thi một operation chỉ với enabled filter.
     */
    @Transactional(readOnly = true)
    public <T> T executeWithEnabledFilterOnly(Supplier<T> operation) {
        return executeWithCustomFilters(() -> {
            filterHelper.disableGlobalFilters(entityManager);
            filterHelper.enableEnabledFilter(entityManager);
            return operation.get();
        });
    }

    /**
     * Enable tất cả global filters cho EntityManager hiện tại.
     */
    protected void enableGlobalFilters() {
        filterHelper.enableGlobalFilters(entityManager);
    }

    /**
     * Disable tất cả global filters cho EntityManager hiện tại.
     */
    protected void disableGlobalFilters() {
        filterHelper.disableGlobalFilters(entityManager);
    }

    /**
     * Enable filters cho một entity class cụ thể.
     */
    protected void enableFiltersForEntityClass(Class<?> entityClass) {
        filterHelper.enableFiltersForEntityClass(entityManager, entityClass);
    }

    /**
     * Thực thi một operation với custom filter configuration.
     */
    protected <T> T executeWithCustomFilters(Supplier<T> operation) {
        try {
            return operation.get();
        } finally {
            // Khôi phục global filters sau khi thực thi
            enableGlobalFilters();
        }
    }

    /**
     * Lấy EntityManager hiện tại.
     */
    protected EntityManager getEntityManager() {
        return entityManager;
    }

    /**
     * Lấy filter helper.
     */
    protected EntityManagerFilterHelper getFilterHelper() {
        return filterHelper;
    }

    /**
     * Kiểm tra xem có đang trong context của một tenant cụ thể không.
     */
    protected boolean isInTenantContext() {
        // Có thể implement logic kiểm tra tenant context
        return true; // Placeholder
    }

    /**
     * Kiểm tra xem có quyền truy cập data của tất cả tenant không.
     */
    protected boolean hasMultiTenantAccess() {
        // Có thể implement logic kiểm tra permission
        return false; // Placeholder
    }

    /**
     * Kiểm tra xem có quyền truy cập deleted data không.
     */
    protected boolean hasDeletedDataAccess() {
        // Có thể implement logic kiểm tra permission
        return false; // Placeholder
    }
}
