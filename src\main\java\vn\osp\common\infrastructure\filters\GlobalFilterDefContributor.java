package vn.osp.common.infrastructure.filters;

import org.hibernate.boot.MetadataBuilder;
import org.hibernate.boot.spi.MetadataBuilderContributor;
import org.hibernate.type.StandardBasicTypes;

public class GlobalFilterDefContributor implements MetadataBuilderContributor {
    @Override
    public void contribute(MetadataBuilder metadataBuilder) {
        metadataBuilder.apply(
                new org.hibernate.engine.spi.FilterDefinition(
                        "tenantFilter",
                        "tenant_id = :tenantId",
                        Map.of("tenantId", StandardBasicTypes.LONG)
                )
        );

        metadataBuilder.applyFilterDefinition(
                new org.hibernate.engine.spi.FilterDefinition(
                        "softDeleteFilter",
                        "deleted = false",
                        Map.of()
                )
        );
    }
}