package vn.osp.common.infrastructure.listeners;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.event.spi.PreInsertEvent;
import org.hibernate.event.spi.PreInsertEventListener;
import org.hibernate.event.spi.PreUpdateEvent;
import org.hibernate.event.spi.PreUpdateEventListener;
import org.hibernate.persister.entity.EntityPersister;
import org.springframework.stereotype.Component;
import vn.osp.common.application.services.RequestContext;
import vn.osp.common.domain.domainentitytypes.HasCreatedBy;
import vn.osp.common.domain.domainentitytypes.HasCreatedByUuid;
import vn.osp.common.domain.domainentitytypes.HasModifiedBy;
import vn.osp.common.domain.domainentitytypes.HasModifiedByUuid;
import vn.osp.common.domain.helpers.UuidHelper;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Hibernate Event Listener tự động điền thông tin audit (Created/Modified)
 * cho các entity khi thực hiện INSERT/UPDATE.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AuditingEventListener implements PreInsertEventListener, PreUpdateEventListener {

    private final RequestContext requestContext;

    @Override
    public boolean onPreInsert(PreInsertEvent event) {
        Object entity = event.getEntity();

        if (entity instanceof HasCreatedBy<?>) {
            setAuditFieldsOnInsert((HasCreatedBy<?>) entity, event);
        }

        if (entity instanceof HasModifiedBy<?>) {
            setAuditFieldsOnInsert((HasModifiedBy<?>) entity, event);
        }

        return false;
    }

    @Override
    public boolean onPreUpdate(PreUpdateEvent event) {
        Object entity = event.getEntity();

        if (entity instanceof HasModifiedBy<?>) {
            setAuditFieldsOnUpdate((HasModifiedBy<?>) entity, event);
        }

        return false;
    }

    private void setAuditFieldsOnInsert(HasCreatedBy<?> entity, PreInsertEvent event) {
        LocalDateTime now = LocalDateTime.now();
        UUID currentUserId = requestContext.getUserId();

        entity.setCreatedAt(now);
        if (UuidHelper.isNotEmpty(currentUserId) && entity instanceof HasCreatedByUuid auditable) {
            auditable.setCreatedBy(currentUserId);
        }

        // Cập nhật giá trị trong Hibernate event
        updatePropertyValue(event.getPersister(), event.getState(), "createdAt", now);
        if (UuidHelper.isNotEmpty(currentUserId) && entity instanceof HasCreatedByUuid) {
            updatePropertyValue(event.getPersister(), event.getState(), "createdBy", currentUserId);
        }

        log.debug("Set audit fields on insert for entity: {} - CreatedAt: {}, CreatedBy: {}",
                entity.getClass().getSimpleName(), now, currentUserId);
    }

    private void setAuditFieldsOnInsert(HasModifiedBy<?> entity, PreInsertEvent event) {
        LocalDateTime now = LocalDateTime.now();
        UUID currentUserId = requestContext.getUserId();

        entity.setModifiedAt(now);
        if (UuidHelper.isNotEmpty(currentUserId) && entity instanceof HasModifiedByUuid auditable) {
            auditable.setModifiedBy(currentUserId);
        }

        // Cập nhật giá trị trong Hibernate event
        updatePropertyValue(event.getPersister(), event.getState(), "modifiedAt", now);
        if (UuidHelper.isNotEmpty(currentUserId) && entity instanceof HasModifiedByUuid) {
            updatePropertyValue(event.getPersister(), event.getState(), "modifiedBy", currentUserId);
        }
    }

    private void setAuditFieldsOnUpdate(HasModifiedBy<?> entity, PreUpdateEvent event) {
        LocalDateTime now = LocalDateTime.now();
        UUID currentUserId = requestContext.getUserId();

        entity.setModifiedAt(now);
        if (UuidHelper.isNotEmpty(currentUserId) && entity instanceof HasModifiedByUuid auditable) {
            auditable.setModifiedBy(currentUserId);
        }

        // Cập nhật giá trị trong Hibernate event
        updatePropertyValue(event.getPersister(), event.getState(), "modifiedAt", now);
        if (UuidHelper.isNotEmpty(currentUserId) && entity instanceof HasModifiedByUuid) {
            updatePropertyValue(event.getPersister(), event.getState(), "modifiedBy", currentUserId);
        }

        log.debug("Set audit fields on update for entity: {} - ModifiedAt: {}, ModifiedBy: {}",
                entity.getClass().getSimpleName(), now, currentUserId);
    }

    /**
     * Cập nhật giá trị property trong Hibernate state array.
     */
    private void updatePropertyValue(EntityPersister persister, Object[] state, String propertyName, Object value) {
        String[] propertyNames = persister.getPropertyNames();
        for (int i = 0; i < propertyNames.length; i++) {
            if (propertyNames[i].equals(propertyName)) {
                state[i] = value;
                break;
            }
        }
    }
}
