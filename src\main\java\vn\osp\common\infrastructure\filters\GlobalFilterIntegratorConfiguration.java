package vn.osp.common.infrastructure.filters;

import lombok.extern.slf4j.Slf4j;
import org.hibernate.boot.SessionFactoryBuilder;
import org.hibernate.boot.spi.MetadataImplementor;
import org.hibernate.boot.spi.SessionFactoryBuilderFactory;
import org.hibernate.boot.spi.SessionFactoryBuilderImplementor;
import org.hibernate.engine.spi.FilterDefinition;
import org.hibernate.mapping.PersistentClass;
import org.hibernate.type.BasicTypeRegistry;
import org.hibernate.type.SqlTypes;
import org.springframework.stereotype.Component;
import vn.osp.common.domain.domainentitytypes.HasTenantId;
import vn.osp.common.domain.helpers.StringHelper;

import java.util.Collections;
import java.util.Map;
import java.util.UUID;

@Component
@Slf4j
public class GlobalFilterIntegratorConfiguration implements SessionFactoryBuilderFactory {
    @Override
    public SessionFactoryBuilder getSessionFactoryBuilder(MetadataImplementor metadata, SessionFactoryBuilderImplementor defaultBuilder) {

        // Lấy BasicTypeRegistry từ BootstrapContext thay vì SessionFactory
        BasicTypeRegistry typeRegistry = metadata
                .getTypeConfiguration()
                .getBasicTypeRegistry();

        metadata.getFilterDefinitions().put(
                GlobalFilterManager.SOFT_DELETE_FILTER,
                new FilterDefinition(
                        GlobalFilterManager.SOFT_DELETE_FILTER,
                        StringHelper.EMPTY,
                        Map.of("isDeleted", typeRegistry.resolve(Boolean.class, SqlTypes.BOOLEAN).getJdbcMapping()),
                        Collections.emptyMap(),
                        false,
                        true
                )
        );

        metadata.getFilterDefinitions().put(
                GlobalFilterManager.TENANT_FILTER,
                new FilterDefinition(
                        GlobalFilterManager.TENANT_FILTER,
                        StringHelper.EMPTY,
                        Map.of("tenantId", typeRegistry.resolve(UUID.class, SqlTypes.UUID).getJdbcMapping()),
                        Collections.emptyMap(),
                        false,
                        true
                )
        );

        metadata.getFilterDefinitions().put(
                GlobalFilterManager.ENABLED_FILTER,
                new FilterDefinition(
                        GlobalFilterManager.ENABLED_FILTER,
                        StringHelper.EMPTY,
                        Map.of("enabled", typeRegistry.resolve(Boolean.class, SqlTypes.BOOLEAN).getJdbcMapping()),
                        Collections.emptyMap(),
                        false,
                        true
                )
        );

        // 2. Attach filter per entity dựa trên marker interface
        for (PersistentClass entity : metadata.getEntityBindings()) {
            Class<?> mappedClass = entity.getMappedClass();
            if (mappedClass != null && HasTenantId.class.isAssignableFrom(mappedClass)) {
                String isDeletedColumnName = entity.getProperty("isDeleted")
                        .getColumns()
                        .getFirst()
                        .getName();
                String softDeleteFilterCondition = isDeletedColumnName + " = :isDeleted";
                entity.addFilter(GlobalFilterManager.SOFT_DELETE_FILTER,
                        softDeleteFilterCondition,
                        true, Collections.emptyMap(), Collections.emptyMap());
            }

            String tenantIdColumnName = entity.getProperty("tenantId")
                    .getColumns()
                    .getFirst()
                    .getName();

            String tenantFilterCondition = "(" + tenantIdColumnName + " = :tenantId OR " + tenantIdColumnName + " IS NULL)";
            if (mappedClass != null && HasTenantId.class.isAssignableFrom(mappedClass)) {
                entity.addFilter(GlobalFilterManager.TENANT_FILTER,
                        tenantFilterCondition,
                        true, Collections.emptyMap(), Collections.emptyMap());
            }

            String enabledColumnName = entity.getProperty("enabled")
                    .getColumns()
                    .getFirst()
                    .getName();
            String enabledFilterCondition = enabledColumnName + " = :enabled";
            if (mappedClass != null && HasTenantId.class.isAssignableFrom(mappedClass)) {
                entity.addFilter(GlobalFilterManager.ENABLED_FILTER,
                        enabledFilterCondition,
                        true, Collections.emptyMap(), Collections.emptyMap());
            }
        }

        return defaultBuilder;
    }

    public class TenantContext {
        private static final ThreadLocal<String> currentTenant = new ThreadLocal<>();

        public static void setTenantId(String tenantId) {
            currentTenant.set(tenantId);
        }

        public static String getTenantId() {
            return currentTenant.get();
        }

        public static void clear() {
            currentTenant.remove();
        }
    }

}