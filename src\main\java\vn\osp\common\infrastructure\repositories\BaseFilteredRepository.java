package vn.osp.common.infrastructure.repositories;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.data.repository.NoRepositoryBean;
import vn.osp.common.infrastructure.filters.EntityManagerFilterHelper;

import java.io.Serializable;
import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;

/**
 * Base repository class tự động áp dụng global filters.
 * Tất cả repository nên extend từ class này để có filtering tự động.
 */
@NoRepositoryBean
@Slf4j
public class BaseFilteredRepository<T, ID extends Serializable> extends SimpleJpaRepository<T, ID> {

    @PersistenceContext
    protected EntityManager entityManager;

    @Autowired
    protected EntityManagerFilterHelper filterHelper;

    public BaseFilteredRepository(Class<T> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
        this.entityManager = entityManager;
    }

    /**
     * Override findAll để tự động enable filters.
     */
    @Override
    public List<T> findAll() {
        enableFiltersForEntityClass();
        return super.findAll();
    }

    /**
     * Override findById để tự động enable filters.
     */
    @Override
    public Optional<T> findById(ID id) {
        enableFiltersForEntityClass();
        return super.findById(id);
    }

    /**
     * Override count để tự động enable filters.
     */
    @Override
    public long count() {
        enableFiltersForEntityClass();
        return super.count();
    }

    /**
     * Override existsById để tự động enable filters.
     */
    @Override
    public boolean existsById(ID id) {
        enableFiltersForEntityClass();
        return super.existsById(id);
    }

    /**
     * Tìm tất cả records bao gồm cả deleted, disabled, và từ tenant khác.
     */
    public List<T> findAllWithoutFilters() {
        return executeWithoutFilters(super::findAll);
    }

    /**
     * Tìm record theo ID bao gồm cả deleted, disabled, và từ tenant khác.
     */
    public Optional<T> findByIdWithoutFilters(ID id) {
        return executeWithoutFilters(() -> super.findById(id));
    }

    /**
     * Đếm tất cả records bao gồm cả deleted, disabled, và từ tenant khác.
     */
    public long countWithoutFilters() {
        return executeWithoutFilters(super::count);
    }

    /**
     * Kiểm tra tồn tại record theo ID bao gồm cả deleted, disabled, và từ tenant khác.
     */
    public boolean existsByIdWithoutFilters(ID id) {
        return executeWithoutFilters(() -> super.existsById(id));
    }

    /**
     * Tìm tất cả records chỉ với soft delete filter (bỏ qua tenant và enabled filter).
     */
    public List<T> findAllOnlyWithSoftDeleteFilter() {
        return executeWithCustomFilters(() -> {
            filterHelper.disableGlobalFilters(entityManager);
            filterHelper.enableSoftDeleteFilter(entityManager);
            return super.findAll();
        });
    }

    /**
     * Tìm tất cả records chỉ với tenant filter (bỏ qua soft delete và enabled filter).
     */
    public List<T> findAllOnlyWithTenantFilter() {
        return executeWithCustomFilters(() -> {
            filterHelper.disableGlobalFilters(entityManager);
            filterHelper.enableTenantFilter(entityManager);
            return super.findAll();
        });
    }

    /**
     * Enable filters cho entity class hiện tại.
     */
    protected void enableFiltersForEntityClass() {
        try {
            filterHelper.enableFiltersForEntityClass(entityManager, getDomainClass());
        } catch (Exception e) {
            log.debug("Could not enable filters for entity class {}: {}", getDomainClass().getSimpleName(), e.getMessage());
        }
    }

    /**
     * Thực thi một operation với tất cả filters được disable.
     */
    protected <R> R executeWithoutFilters(Supplier<R> operation) {
        return filterHelper.executeWithoutFilters(entityManager, operation);
    }

    /**
     * Thực thi một operation với custom filter configuration.
     */
    protected <R> R executeWithCustomFilters(Supplier<R> operation) {
        try {
            return operation.get();
        } finally {
            // Khôi phục filters sau khi thực thi
            enableFiltersForEntityClass();
        }
    }

    /**
     * Lấy EntityManager hiện tại.
     */
    protected EntityManager getEntityManager() {
        return entityManager;
    }

    /**
     * Lấy filter helper.
     */
    protected EntityManagerFilterHelper getFilterHelper() {
        return filterHelper;
    }
}
