package vn.osp.common.infrastructure.filters;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.Filter;
import org.hibernate.Session;
import org.springframework.stereotype.Component;
import vn.osp.common.application.services.RequestContext;
import vn.osp.common.domain.domainentitytypes.HasEnabled;
import vn.osp.common.domain.domainentitytypes.HasTenantId;
import vn.osp.common.domain.domainentitytypes.SoftDelete;
import vn.osp.common.domain.helpers.UuidHelper;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.function.Supplier;

/**
 * Global Filter Manager tự động enable/disable các filter cho Hibernate Session.
 * Không cần annotate từng entity, tự động áp dụng filter dựa trên interface.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class GlobalFilterManager {

    public static final String SOFT_DELETE_FILTER = "softDeleteFilter";
    public static final String TENANT_FILTER = "tenantFilter";
    public static final String ENABLED_FILTER = "enabledFilter";

    private final RequestContext requestContext;

    /**
     * Map chứa các default parameter resolvers cho từng filter.
     * Đây là implementation của parameterResolverMap concept.
     */
    private static final Map<String, Map<String, Supplier<Object>>> FILTER_DEFAULT_PARAMETERS = new HashMap<>();

    static {
        // Cấu hình default parameters cho softDeleteFilter
        Map<String, Supplier<Object>> softDeleteDefaults = new HashMap<>();
        softDeleteDefaults.put("isDeleted", () -> false); // Mặc định chỉ lấy records chưa bị xóa
        FILTER_DEFAULT_PARAMETERS.put(SOFT_DELETE_FILTER, softDeleteDefaults);

        // Cấu hình default parameters cho enabledFilter
        Map<String, Supplier<Object>> enabledDefaults = new HashMap<>();
        enabledDefaults.put("enabled", () -> true); // Mặc định chỉ lấy records được enable
        FILTER_DEFAULT_PARAMETERS.put(ENABLED_FILTER, enabledDefaults);

        log.debug("Initialized filter default parameters: {}", FILTER_DEFAULT_PARAMETERS.keySet());
    }

    /**
     * Enable tất cả global filters cho session hiện tại.
     */
    public void enableGlobalFilters(Session session) {
        enableSoftDeleteFilter(session);
        enableTenantFilter(session);
        enableEnabledFilter(session);
    }

    /**
     * Disable tất cả global filters cho session hiện tại.
     */
    public void disableGlobalFilters(Session session) {
        disableFilter(session, SOFT_DELETE_FILTER);
        disableFilter(session, TENANT_FILTER);
        disableFilter(session, ENABLED_FILTER);
    }

    /**
     * Enable soft delete filter - chỉ lấy records chưa bị xóa.
     * Sử dụng giá trị mặc định false cho isDeleted parameter.
     */
    public void enableSoftDeleteFilter(Session session) {
        try {
            Filter filter = session.enableFilter(SOFT_DELETE_FILTER);
            // Set giá trị mặc định false cho isDeleted parameter
            filter.setParameter("isDeleted", getDefaultSoftDeleteValue());
            log.debug("Enabled soft delete filter for session with default value: false");
        } catch (Exception e) {
            log.debug("Soft delete filter not available or already enabled: {}", e.getMessage());
        }
    }

    /**
     * Trả về giá trị mặc định cho soft delete filter.
     * Có thể được override trong subclass nếu cần custom logic.
     */
    protected boolean getDefaultSoftDeleteValue() {
        return false; // Mặc định chỉ lấy records chưa bị xóa
    }

    /**
     * Enable tenant filter - chỉ lấy records của tenant hiện tại.
     */
    public void enableTenantFilter(Session session) {
        try {
            UUID currentTenantId = requestContext.getTenantId();
            if (UuidHelper.isNotEmpty(currentTenantId)) {
                Filter filter = session.enableFilter(TENANT_FILTER);
                filter.setParameter("tenantId", currentTenantId);
                log.debug("Enabled tenant filter for session with tenantId: {}", currentTenantId);
            } else {
                log.debug("No current tenant ID, skipping tenant filter");
            }
        } catch (Exception e) {
            log.debug("Tenant filter not available or already enabled: {}", e.getMessage());
        }
    }

    /**
     * Enable enabled filter - chỉ lấy records đang được kích hoạt.
     */
    public void enableEnabledFilter(Session session) {
        try {
            Filter filter = session.enableFilter(ENABLED_FILTER);
            filter.setParameter("enabled", true);
            log.debug("Enabled 'enabled' filter for session");
        } catch (Exception e) {
            log.debug("Enabled filter not available or already enabled: {}", e.getMessage());
        }
    }

    /**
     * Disable một filter cụ thể.
     */
    public void disableFilter(Session session, String filterName) {
        try {
            session.disableFilter(filterName);
            log.debug("Disabled filter: {}", filterName);
        } catch (Exception e) {
            log.debug("Failed to disable filter {}: {}", filterName, e.getMessage());
        }
    }

    /**
     * Enable filter cho một entity class cụ thể dựa trên interface.
     */
    public void enableFiltersForEntityClass(Session session, Class<?> entityClass) {
        // Soft Delete Filter
        if (SoftDelete.class.isAssignableFrom(entityClass)) {
            enableSoftDeleteFilter(session);
        }

        // Tenant Filter
        if (HasTenantId.class.isAssignableFrom(entityClass)) {
            enableTenantFilter(session);
        }

        // Enabled Filter
        if (HasEnabled.class.isAssignableFrom(entityClass)) {
            enableEnabledFilter(session);
        }
    }

    /**
     * Disable filter cho một entity class cụ thể dựa trên interface.
     */
    public void disableFiltersForEntityClass(Session session, Class<?> entityClass) {
        // Soft Delete Filter
        if (SoftDelete.class.isAssignableFrom(entityClass)) {
            session.disableFilter(SOFT_DELETE_FILTER);
        }

        // Tenant Filter
        if (HasTenantId.class.isAssignableFrom(entityClass)) {
            session.disableFilter(TENANT_FILTER);
        }

        // Enabled Filter
        if (HasEnabled.class.isAssignableFrom(entityClass)) {
            session.disableFilter(ENABLED_FILTER);
        }
    }

    /**
     * Thực thi một block code với tất cả filters được disable.
     */
    public <T> T executeWithoutFilters(Session session, Supplier<T> supplier) {
        // Lưu trạng thái filter hiện tại
        boolean softDeleteEnabled = isFilterEnabled(session, SOFT_DELETE_FILTER);
        boolean tenantEnabled = isFilterEnabled(session, TENANT_FILTER);
        boolean enabledEnabled = isFilterEnabled(session, ENABLED_FILTER);

        try {
            // Disable tất cả filters
            disableGlobalFilters(session);

            // Thực thi code
            return supplier.get();
        } finally {
            // Khôi phục trạng thái filter
            if (softDeleteEnabled) enableSoftDeleteFilter(session);
            if (tenantEnabled) enableTenantFilter(session);
            if (enabledEnabled) enableEnabledFilter(session);
        }
    }

    /**
     * Thực thi một block code với tất cả filters được disable (void return).
     */
    public void executeWithoutFilters(Session session, Runnable runnable) {
        executeWithoutFilters(session, () -> {
            runnable.run();
            return null;
        });
    }

    /**
     * Thực thi một block code với filter được disable.
     */
    public <T> T executeWithoutFilter(Session session, String filterName, Supplier<T> supplier) {
        // Lưu trạng thái filter hiện tại
        boolean enabled = isFilterEnabled(session, filterName);

        try {
            // Disable tất cả filters
            disableFilter(session, filterName);

            // Thực thi code
            return supplier.get();
        } finally {
            // Khôi phục trạng thái filter
            if (enabled) {
                switch (filterName) {
                    case SOFT_DELETE_FILTER -> enableSoftDeleteFilter(session);
                    case TENANT_FILTER -> enableTenantFilter(session);
                    case ENABLED_FILTER -> enableEnabledFilter(session);
                    default -> log.warn("Unknown filter name: {}", filterName);
                }
            }
        }
    }

    /**
     * Thực thi một block code với filter được disable (void return).
     */
    public void executeWithoutFilter(Session session, String filterName, Runnable runnable) {
        executeWithoutFilter(session, filterName, () -> {
            runnable.run();
            return null;
        });
    }

    /**
     * Kiểm tra xem một filter có đang được enable hay không.
     */
    private boolean isFilterEnabled(Session session, String filterName) {
        try {
            return session.getEnabledFilter(filterName) != null;
        } catch (Exception e) {
            return false;
        }
    }
}
