package vn.osp.common.infrastructure.filters;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.hibernate.Session;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.test.context.ContextConfiguration;
import vn.osp.common.domain.entities.ExampleEntity;
import vn.osp.common.infrastructure.repositories.ExampleEntityRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Test class để verify global filters hoạt động với ExampleEntityRepository
 */
@DataJpaTest
@ContextConfiguration(classes = GlobalFilterTestConfiguration.class)
public class ExampleEntityRepositoryFilterTest {

    @Autowired
    private TestEntityManager testEntityManager;

    @Autowired
    private ExampleEntityRepository repository;

    @PersistenceContext
    private EntityManager entityManager;

    private UUID tenant1Id;
    private UUID tenant2Id;
    private UUID user1Id;

    private ExampleEntity entity1; // tenant1, enabled, not deleted
    private ExampleEntity entity2; // tenant1, disabled, not deleted
    private ExampleEntity entity3; // tenant1, enabled, deleted
    private ExampleEntity entity4; // tenant2, enabled, not deleted
    private ExampleEntity entity5; // no tenant, enabled, not deleted

    @BeforeEach
    void setUp() {
        // Initialize IDs
        tenant1Id = UUID.randomUUID();
        tenant2Id = UUID.randomUUID();
        user1Id = UUID.randomUUID();

        // Create test entities
        entity1 = createEntity("REPO_001", "Repository Entity 1", tenant1Id, true, false);
        entity2 = createEntity("REPO_002", "Repository Entity 2", tenant1Id, false, false);
        entity3 = createEntity("REPO_003", "Repository Entity 3", tenant1Id, true, true);
        entity4 = createEntity("REPO_004", "Repository Entity 4", tenant2Id, true, false);
        entity5 = createEntity("REPO_005", "Repository Entity 5", null, true, false);

        // Save entities
        testEntityManager.persistAndFlush(entity1);
        testEntityManager.persistAndFlush(entity2);
        testEntityManager.persistAndFlush(entity3);
        testEntityManager.persistAndFlush(entity4);
        testEntityManager.persistAndFlush(entity5);

        // Clear persistence context
        testEntityManager.clear();
    }

    private ExampleEntity createEntity(String code, String title, UUID tenantId, boolean enabled, boolean deleted) {
        ExampleEntity entity = new ExampleEntity();
        entity.setCode(code);
        entity.setTitle(title);
        entity.setTenantId(tenantId);
        entity.setEnabled(enabled);
        entity.setDeleted(deleted);
        entity.setCreatedAt(LocalDateTime.now());
        entity.setCreatedBy(user1Id);

        if (deleted) {
            entity.setDeletedAt(LocalDateTime.now());
            entity.setDeletedBy(user1Id);
        }

        return entity;
    }

    @Test
    void testFindByCodeWithoutFilters() {
        // When: No filters applied
        Optional<ExampleEntity> result = repository.findByCode("REPO_003");

        // Then: Should find deleted entity
        assertThat(result).isPresent();
        assertThat(result.get().getCode()).isEqualTo("REPO_003");
        assertThat(result.get().isDeleted()).isTrue();
    }

    @Test
    void testFindByCodeWithSoftDeleteFilter() {
        // Given: Apply soft delete filter
        Session session = entityManager.unwrap(Session.class);
        session.enableFilter("softDeleteFilter").setParameter("isDeleted", false);

        // When: Search for deleted entity
        Optional<ExampleEntity> result = repository.findByCode("REPO_003");

        // Then: Should not find deleted entity
        assertThat(result).isEmpty();

        // When: Search for non-deleted entity
        Optional<ExampleEntity> result2 = repository.findByCode("REPO_001");

        // Then: Should find non-deleted entity
        assertThat(result2).isPresent();
        assertThat(result2.get().getCode()).isEqualTo("REPO_001");
        assertThat(result2.get().isDeleted()).isFalse();
    }

    @Test
    void testFindAllEnabledWithoutFilters() {
        // When: No filters applied
        List<ExampleEntity> result = repository.findAllEnabled();

        // Then: Should return all enabled entities (including deleted one)
        assertThat(result).hasSize(3); // entity1, entity3 (deleted but enabled), entity4, entity5
        assertThat(result).extracting(ExampleEntity::getCode)
                .contains("REPO_001", "REPO_003", "REPO_004", "REPO_005");
    }

    @Test
    void testFindAllEnabledWithSoftDeleteFilter() {
        // Given: Apply soft delete filter
        Session session = entityManager.unwrap(Session.class);
        session.enableFilter("softDeleteFilter").setParameter("isDeleted", false);

        // When: Find all enabled entities
        List<ExampleEntity> result = repository.findAllEnabled();

        // Then: Should exclude deleted entity
        assertThat(result).hasSize(3); // entity1, entity4, entity5 (excluding deleted entity3)
        assertThat(result).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("REPO_001", "REPO_004", "REPO_005");
    }

    @Test
    void testFindByTenantIdWithoutFilters() {
        // When: No filters applied
        List<ExampleEntity> result = repository.findByTenantId(tenant1Id);

        // Then: Should return all tenant1 entities (including deleted and disabled)
        assertThat(result).hasSize(3); // entity1, entity2, entity3
        assertThat(result).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("REPO_001", "REPO_002", "REPO_003");
    }

    @Test
    void testFindByTenantIdWithFilters() {
        // Given: Apply multiple filters
        Session session = entityManager.unwrap(Session.class);
        session.enableFilter("softDeleteFilter").setParameter("isDeleted", false);
        session.enableFilter("enabledFilter").setParameter("enabled", true);

        // When: Find by tenant1
        List<ExampleEntity> result = repository.findByTenantId(tenant1Id);

        // Then: Should return only entity1 (tenant1, enabled, not deleted)
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getCode()).isEqualTo("REPO_001");
        assertThat(result.get(0).isEnabled()).isTrue();
        assertThat(result.get(0).isDeleted()).isFalse();
    }

    @Test
    void testFindAllIncludingDeletedWithTenantFilter() {
        // Given: Apply tenant filter only
        Session session = entityManager.unwrap(Session.class);
        session.enableFilter("tenantFilter").setParameter("tenantId", tenant1Id);

        // When: Find all (including deleted)
        List<ExampleEntity> result = repository.findAllIncludingDeleted();

        // Then: Should return only tenant1 entities and null tenant entities
        assertThat(result).hasSize(4); // entity1, entity2, entity3 (tenant1) + entity5 (null tenant)
        assertThat(result).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("REPO_001", "REPO_002", "REPO_003", "REPO_005");

        // Verify no tenant2 entities
        assertThat(result).noneMatch(e -> tenant2Id.equals(e.getTenantId()));
    }

    @Test
    void testFindAllWithCombinedFilters() {
        // Given: Apply all filters for tenant1
        Session session = entityManager.unwrap(Session.class);
        session.enableFilter("softDeleteFilter").setParameter("isDeleted", false);
        session.enableFilter("tenantFilter").setParameter("tenantId", tenant1Id);
        session.enableFilter("enabledFilter").setParameter("enabled", true);

        // When: Find all
        List<ExampleEntity> result = repository.findAllIncludingDeleted();

        // Then: Should return only entity1 and entity5 (tenant1 or null, enabled, not deleted)
        assertThat(result).hasSize(2);
        assertThat(result).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("REPO_001", "REPO_005");

        // Verify properties
        result.forEach(entity -> {
            assertThat(entity.isEnabled()).isTrue();
            assertThat(entity.isDeleted()).isFalse();
            assertThat(entity.getTenantId()).satisfiesAnyOf(
                    tenantId -> assertThat(tenantId).isEqualTo(tenant1Id),
                    tenantId -> assertThat(tenantId).isNull()
            );
        });
    }

    @Test
    void testStandardJpaMethodsWithFilters() {
        // Given: Apply filters
        Session session = entityManager.unwrap(Session.class);
        session.enableFilter("softDeleteFilter").setParameter("isDeleted", false);
        session.enableFilter("enabledFilter").setParameter("enabled", true);

        // When: Use standard JPA methods
        List<ExampleEntity> allEntities = repository.findAll();
        long count = repository.count();

        // Then: Filters should be applied to standard JPA methods too
        assertThat(allEntities).hasSize(3); // entity1, entity4, entity5 (enabled, not deleted)
        assertThat(count).isEqualTo(3);

        assertThat(allEntities).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("REPO_001", "REPO_004", "REPO_005");

        allEntities.forEach(entity -> {
            assertThat(entity.isEnabled()).isTrue();
            assertThat(entity.isDeleted()).isFalse();
        });
    }
}
