package vn.osp.common.infrastructure.listeners;

import org.hibernate.boot.MetadataBuilder;
import org.hibernate.boot.spi.MetadataBuilderContributor;
import org.hibernate.engine.spi.FilterDefinition;
import vn.osp.common.infrastructure.filters.GlobalFilterManager;

import java.util.Map;
import java.util.UUID;

public class GlobalFilterMetadataContributor implements MetadataBuilderContributor {

    @Override
    public void contribute(MetadataBuilder metadataBuilder) {
        // Soft Delete Filter
        metadataBuilder.applyFilterDefinition(
                new FilterDefinition(
                        GlobalFilterManager.SOFT_DELETE_FILTER,
                        "isDeleted = false",
                        Map.of()
                )
        );

        // Tenant Filter
        metadataBuilder.applyFilterDefinition(
                new FilterDefinition(
                        GlobalFilterManager.TENANT_FILTER,
                        "tenantId = :tenantId OR tenantId IS NULL",
                        Map.of("tenantId", UUID.class)
                )
        );

        // Enabled Filter
        metadataBuilder.applyFilterDefinition(
                new FilterDefinition(
                        GlobalFilterManager.ENABLED_FILTER,
                        "enabled = true",
                        Map.of()
                )
        );
    }
}

