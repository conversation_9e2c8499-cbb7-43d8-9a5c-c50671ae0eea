package vn.osp.common.infrastructure.filters;

import org.hibernate.boot.SessionFactoryBuilder;
import org.hibernate.boot.spi.MetadataImplementor;
import org.hibernate.boot.spi.SessionFactoryBuilderFactory;
import org.hibernate.boot.spi.SessionFactoryBuilderImplementor;
import org.hibernate.engine.spi.FilterDefinition;
import org.hibernate.mapping.PersistentClass;
import org.hibernate.resource.beans.spi.ManagedBean;
import org.hibernate.type.SqlTypes;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.function.Supplier;

/**
 * Factory để customize SessionFactory builder và add filters
 */
public class TestSessionFactoryBuilderFactory implements SessionFactoryBuilderFactory {

    @Override
    public SessionFactoryBuilder getSessionFactoryBuilder(MetadataImplementor metadata, SessionFactoryBuilderImplementor defaultBuilder) {
        System.out.println("🏗️ SessionFactoryBuilderFactory called");
        
        // Add filter definitions to metadata before SessionFactory is built
        var typeRegistry = metadata.getTypeConfiguration().getBasicTypeRegistry();

        // Tạo parameterResolverMap với giá trị mặc định cho softDeleteFilter
        // Kiểu dữ liệu đúng: Map<String, ManagedBean<? extends Supplier<?>>>
        Map<String, ManagedBean<? extends Supplier<?>>> softDeleteParameterResolvers = new HashMap<>();

        // Tạo ManagedBean wrapper cho Supplier
        ManagedBean<Supplier<Boolean>> isDeletedResolver = new ManagedBean<Supplier<Boolean>>() {
            @Override
            @SuppressWarnings("unchecked")
            public Class<Supplier<Boolean>> getBeanClass() {
                return (Class<Supplier<Boolean>>) (Class<?>) Supplier.class;
            }

            @Override
            public Supplier<Boolean> getBeanInstance() {
                return () -> false; // Mặc định trả về false
            }
        };

        softDeleteParameterResolvers.put("isDeleted", isDeletedResolver);

        FilterDefinition softDeleteFilter = new FilterDefinition(
                "softDeleteFilter",
                "",
                Map.of("isDeleted", typeRegistry.resolve(Boolean.class, SqlTypes.BOOLEAN).getJdbcMapping()),
                softDeleteParameterResolvers, // Sử dụng parameterResolverMap với kiểu đúng
                true,
                true
        );
        
        FilterDefinition tenantFilter = new FilterDefinition(
                "tenantFilter",
                "",
                Map.of("tenantId", typeRegistry.resolve(UUID.class, SqlTypes.UUID).getJdbcMapping()),
                Collections.emptyMap(),
                false,
                true
        );
        
        FilterDefinition enabledFilter = new FilterDefinition(
                "enabledFilter", 
                "",
                Map.of("enabled", typeRegistry.resolve(Boolean.class, SqlTypes.BOOLEAN).getJdbcMapping()),
                Collections.emptyMap(),
                false,
                true
        );

        metadata.getFilterDefinitions().put("softDeleteFilter", softDeleteFilter);
        metadata.getFilterDefinitions().put("tenantFilter", tenantFilter);
        metadata.getFilterDefinitions().put("enabledFilter", enabledFilter);
        
        // Attach filters to ExampleEntity only
        for (PersistentClass entity : metadata.getEntityBindings()) {
            Class<?> mappedClass = entity.getMappedClass();
            
            if (mappedClass != null && mappedClass.getSimpleName().equals("ExampleEntity")) {
                var isDeletedColumnName = entity.getProperty("isDeleted")
                        .getColumns()
                        .getFirst()
                        .getName();
                String softDeleteFilterCondition = isDeletedColumnName + " = :isDeleted";
                // Add filters to ExampleEntity
                entity.addFilter("softDeleteFilter",
                        softDeleteFilterCondition,
                        true, Collections.emptyMap(), Collections.emptyMap());

                // Lấy tên cột vật lý của property tenantId
                var tenantIdColumnName = entity.getProperty("tenantId")
                        .getColumns()
                        .getFirst()
                        .getName();

                String tenantFilterCondition = "(" + tenantIdColumnName + " = :tenantId OR " + tenantIdColumnName + " IS NULL)";
                entity.addFilter("tenantFilter", 
                        tenantFilterCondition,
                        true, Collections.emptyMap(), Collections.emptyMap());

                var enabledColumnName = entity.getProperty("enabled")
                        .getColumns()
                        .getFirst()
                        .getName();
                String enabledFilterCondition = enabledColumnName + " = :enabled";
                entity.addFilter("enabledFilter",
                        enabledFilterCondition, 
                        true, Collections.emptyMap(), Collections.emptyMap());
                        
                System.out.println("✅ Added filters to ExampleEntity via SessionFactoryBuilderFactory: " + mappedClass.getName());
            } else if (mappedClass != null) {
                System.out.println("ℹ️ Skipped filters for: " + mappedClass.getSimpleName());
            }
        }
        
        System.out.println("✅ Filters added via SessionFactoryBuilderFactory: " + metadata.getFilterDefinitions().keySet());
        
        return defaultBuilder;
    }
}
