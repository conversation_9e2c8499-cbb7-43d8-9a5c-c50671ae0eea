package vn.osp.common.infrastructure.repositories;

import jakarta.persistence.EntityManager;
import org.hibernate.Session;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.transaction.annotation.Transactional;
import vn.osp.common.domain.entities.ExampleEntity;
import vn.osp.common.infrastructure.filters.GlobalFilterTestConfiguration;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Test để kiểm tra Global Filters hoạt động với ExampleEntityRepository interface
 * Test này sử dụng trực tiếp repository methods thay vì simulate qua EntityManager
 */
@SpringBootTest
@ContextConfiguration(classes = GlobalFilterTestConfiguration.class)
@Transactional
class ExampleEntityRepositoryGlobalFilterTest {

    @Autowired
    private EntityManager entityManager;

    @Autowired
    private ExampleEntityRepository repository;

    private UUID tenant1Id;
    private UUID tenant2Id;

    @BeforeEach
    void setUp() {
        tenant1Id = UUID.randomUUID();
        tenant2Id = UUID.randomUUID();

        // Create test data
        createTestData();
        
        // Clear any existing filters
        Session session = entityManager.unwrap(Session.class);
        session.disableFilter("softDeleteFilter");
        session.disableFilter("tenantFilter");
        session.disableFilter("enabledFilter");
        
        entityManager.flush();
        entityManager.clear();
    }

    private void createTestData() {
        // Entity 1: tenant1, enabled, not deleted
        ExampleEntity entity1 = new ExampleEntity();
        entity1.setCode("ENTITY_001");
        entity1.setTitle("Test Entity 1");
        entity1.setEnabled(true);
        entity1.setDeleted(false);
        entity1.setTenantId(tenant1Id);
        entityManager.persist(entity1);

        // Entity 2: tenant1, disabled, not deleted
        ExampleEntity entity2 = new ExampleEntity();
        entity2.setCode("ENTITY_002");
        entity2.setTitle("Test Entity 2");
        entity2.setEnabled(false);
        entity2.setDeleted(false);
        entity2.setTenantId(tenant1Id);
        entityManager.persist(entity2);

        // Entity 3: tenant1, enabled, deleted
        ExampleEntity entity3 = new ExampleEntity();
        entity3.setCode("ENTITY_003");
        entity3.setTitle("Test Entity 3");
        entity3.setEnabled(true);
        entity3.setDeleted(true);
        entity3.setTenantId(tenant1Id);
        entityManager.persist(entity3);

        // Entity 4: tenant2, enabled, not deleted
        ExampleEntity entity4 = new ExampleEntity();
        entity4.setCode("ENTITY_004");
        entity4.setTitle("Test Entity 4");
        entity4.setEnabled(true);
        entity4.setDeleted(false);
        entity4.setTenantId(tenant2Id);
        entityManager.persist(entity4);

        // Entity 5: null tenant, enabled, not deleted
        ExampleEntity entity5 = new ExampleEntity();
        entity5.setCode("ENTITY_005");
        entity5.setTitle("Test Entity 5");
        entity5.setEnabled(true);
        entity5.setDeleted(false);
        entity5.setTenantId(null);
        entityManager.persist(entity5);
        
        entityManager.flush();
    }

    // ============== Repository Interface Direct Tests ==============

    @Test
    void testRepositoryFindByCodeWithoutFilters() {
        // When: No filters applied
        Optional<ExampleEntity> result = repository.findByCode("ENTITY_003");

        // Then: Should find deleted entity
        assertThat(result).isPresent();
        assertThat(result.get().getCode()).isEqualTo("ENTITY_003");
        assertThat(result.get().isDeleted()).isTrue();
    }

    @Test
    void testRepositoryFindByCodeWithSoftDeleteFilter() {
        // Given: Apply soft delete filter
        Session session = entityManager.unwrap(Session.class);
        session.enableFilter("softDeleteFilter").setParameter("isDeleted", false);

        // When: Search for deleted entity
        Optional<ExampleEntity> result = repository.findByCode("ENTITY_003");

        // Then: Should not find deleted entity
        assertThat(result).isEmpty();

        // When: Search for non-deleted entity
        Optional<ExampleEntity> result2 = repository.findByCode("ENTITY_001");

        // Then: Should find non-deleted entity
        assertThat(result2).isPresent();
        assertThat(result2.get().getCode()).isEqualTo("ENTITY_001");
        assertThat(result2.get().isDeleted()).isFalse();
    }

    @Test
    void testRepositoryFindAllEnabledWithoutFilters() {
        // When: No filters applied
        List<ExampleEntity> result = repository.findAllEnabled();

        // Then: Should return all enabled entities (including deleted one)
        assertThat(result).hasSize(4); // entity1, entity3 (deleted but enabled), entity4, entity5
        assertThat(result).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("ENTITY_001", "ENTITY_003", "ENTITY_004", "ENTITY_005");
    }

    @Test
    void testRepositoryFindAllEnabledWithSoftDeleteFilter() {
        // Given: Apply soft delete filter
        Session session = entityManager.unwrap(Session.class);
        session.enableFilter("softDeleteFilter").setParameter("isDeleted", false);

        // When: Find all enabled entities
        List<ExampleEntity> result = repository.findAllEnabled();

        // Then: Should exclude deleted entity
        assertThat(result).hasSize(3); // entity1, entity4, entity5 (excluding deleted entity3)
        assertThat(result).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("ENTITY_001", "ENTITY_004", "ENTITY_005");
        
        // Verify all returned entities are not deleted
        result.forEach(entity -> assertThat(entity.isDeleted()).isFalse());
    }

    @Test
    void testRepositoryFindByTenantIdWithoutFilters() {
        // When: No filters applied
        List<ExampleEntity> result = repository.findByTenantId(tenant1Id);

        // Then: Should return all tenant1 entities (including deleted and disabled)
        assertThat(result).hasSize(3); // entity1, entity2, entity3
        assertThat(result).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("ENTITY_001", "ENTITY_002", "ENTITY_003");
    }

    @Test
    void testRepositoryFindByTenantIdWithFilters() {
        // Given: Apply multiple filters
        Session session = entityManager.unwrap(Session.class);
        session.enableFilter("softDeleteFilter").setParameter("isDeleted", false);
        session.enableFilter("enabledFilter").setParameter("enabled", true);

        // When: Find by tenant1
        List<ExampleEntity> result = repository.findByTenantId(tenant1Id);

        // Then: Should return only entity1 (tenant1, enabled, not deleted)
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getCode()).isEqualTo("ENTITY_001");
        assertThat(result.get(0).isEnabled()).isTrue();
        assertThat(result.get(0).isDeleted()).isFalse();
        assertThat(result.get(0).getTenantId()).isEqualTo(tenant1Id);
    }

    @Test
    void testRepositoryFindAllIncludingDeletedWithTenantFilter() {
        // Given: Apply tenant filter only
        Session session = entityManager.unwrap(Session.class);
        session.enableFilter("tenantFilter").setParameter("tenantId", tenant1Id);

        // When: Find all (including deleted)
        List<ExampleEntity> result = repository.findAllIncludingDeleted();

        // Then: Should return only tenant1 entities and null tenant entities
        assertThat(result).hasSize(4); // entity1, entity2, entity3 (tenant1) + entity5 (null tenant)
        assertThat(result).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("ENTITY_001", "ENTITY_002", "ENTITY_003", "ENTITY_005");

        // Verify no tenant2 entities
        assertThat(result).noneMatch(e -> tenant2Id.equals(e.getTenantId()));
    }

    @Test
    void testRepositoryFindAllWithCombinedFilters() {
        // Given: Apply all filters for tenant1
        Session session = entityManager.unwrap(Session.class);
        session.enableFilter("softDeleteFilter").setParameter("isDeleted", false);
        session.enableFilter("tenantFilter").setParameter("tenantId", tenant1Id);
        session.enableFilter("enabledFilter").setParameter("enabled", true);

        // When: Find all
        List<ExampleEntity> result = repository.findAllIncludingDeleted();

        // Then: Should return only entity1 and entity5 (tenant1 or null, enabled, not deleted)
        assertThat(result).hasSize(2);
        assertThat(result).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("ENTITY_001", "ENTITY_005");

        // Verify properties
        result.forEach(entity -> {
            assertThat(entity.isEnabled()).isTrue();
            assertThat(entity.isDeleted()).isFalse();
            assertThat(entity.getTenantId()).satisfiesAnyOf(
                    tenantId -> assertThat(tenantId).isEqualTo(tenant1Id),
                    tenantId -> assertThat(tenantId).isNull()
            );
        });
    }

    @Test
    void testRepositoryStandardJpaMethodsWithFilters() {
        // Given: Apply filters
        Session session = entityManager.unwrap(Session.class);
        session.enableFilter("softDeleteFilter").setParameter("isDeleted", false);
        session.enableFilter("enabledFilter").setParameter("enabled", true);

        // When: Use standard JPA methods
        List<ExampleEntity> allEntities = repository.findAll();
        long count = repository.count();

        // Then: Filters should be applied to standard JPA methods too
        assertThat(allEntities).hasSize(3); // entity1, entity4, entity5 (enabled, not deleted)
        assertThat(count).isEqualTo(3);

        assertThat(allEntities).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("ENTITY_001", "ENTITY_004", "ENTITY_005");

        allEntities.forEach(entity -> {
            assertThat(entity.isEnabled()).isTrue();
            assertThat(entity.isDeleted()).isFalse();
        });
    }

    @Test
    void testRepositoryCustomQueriesInheritGlobalFilters() {
        // Given: Apply filters
        Session session = entityManager.unwrap(Session.class);
        session.enableFilter("softDeleteFilter").setParameter("isDeleted", false);
        session.enableFilter("tenantFilter").setParameter("tenantId", tenant1Id);

        // When: Use custom repository queries
        Optional<ExampleEntity> byCode = repository.findByCode("ENTITY_001");
        List<ExampleEntity> allEnabled = repository.findAllEnabled();
        List<ExampleEntity> byTenant = repository.findByTenantId(tenant1Id);
        List<ExampleEntity> allIncludingDeleted = repository.findAllIncludingDeleted();

        // Then: All repository methods should respect global filters
        assertThat(byCode).isPresent();
        assertThat(byCode.get().getCode()).isEqualTo("ENTITY_001");

        // allEnabled should only return tenant1 or null tenant, enabled, not deleted
        assertThat(allEnabled).hasSize(2); // entity1, entity5
        assertThat(allEnabled).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("ENTITY_001", "ENTITY_005");

        // byTenant should only return tenant1, not deleted entities
        assertThat(byTenant).hasSize(1); // only entity1
        assertThat(byTenant.get(0).getCode()).isEqualTo("ENTITY_001");

        // allIncludingDeleted should only return tenant1 or null tenant, not deleted
        assertThat(allIncludingDeleted).hasSize(2); // entity1, entity5
        assertThat(allIncludingDeleted).extracting(ExampleEntity::getCode)
                .containsExactlyInAnyOrder("ENTITY_001", "ENTITY_005");
    }
}
