package vn.osp.common.infrastructure.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation để explicitly enable global filters cho một method.
 * Sử dụng khi cần đảm bảo filters được áp dụng cho method cụ thể.
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface EnableGlobalFilters {
    
    /**
     * Danh sách filters cần enable. Nếu empty thì enable tất cả.
     */
    String[] filters() default {};
    
    /**
     * Mô tả lý do enable filters.
     */
    String reason() default "";
}
