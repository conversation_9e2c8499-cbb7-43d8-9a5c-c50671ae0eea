package vn.osp.common.infrastructure.services;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.osp.common.application.models.TenantDto;
import vn.osp.common.application.services.RequestContext;
import vn.osp.common.application.services.TenantClientService;
import vn.osp.common.domain.helpers.StringHelper;
import vn.osp.common.infrastructure.helpers.HttpHelper;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class RequestContextImpl implements RequestContext {
    // todo: dung thread local luu thong tin user
    private final HttpServletRequest request;
    private final TenantClientService tenantClientService;

    private static final ThreadLocal<UUID> tenantId = new ThreadLocal<>();

    @Override
    public UUID getTenantId() {
        UUID tenantId = null;
        String tenantName = HttpHelper.getSubdomain(request);
        if (StringHelper.isNotEmpty(tenantName)) {
            TenantDto tenant = tenantClientService.getTenantByName(tenantName);
            if (tenant != null) {
                tenantId = tenant.getId();
            }
        }

        return tenantId;
    }

    @Override
    public UUID getUserId() {
        // TODO: implement khi tích hợp Spring Security
        return null;
    }

    @Override
    public String getUsername() {
        // TODO: implement khi tích hợp Spring Security
        return null;
    }

    @Override
    public List<String> getRoles() {
        // TODO: implement khi tích hợp Spring Securitys
        return List.of();
    }
}
