package vn.osp.common.infrastructure.listeners;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.event.spi.*;
import org.hibernate.persister.entity.EntityPersister;
import org.springframework.stereotype.Component;
import vn.osp.common.application.services.RequestContext;
import vn.osp.common.domain.domainentitytypes.HasTenantId;
import vn.osp.common.domain.helpers.UuidHelper;

import java.util.UUID;

/**
 * Hibernate Event Listener tự động set TenantId cho các entity
 * khi thực hiện INSERT và validate TenantId khi thực hiện UPDATE/DELETE.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class MultiTenantEventListener implements PreInsertEventListener, PreUpdateEventListener, PreDeleteEventListener {

    private final RequestContext requestContext;

    @Override
    public boolean onPreInsert(PreInsertEvent event) {
        Object entity = event.getEntity();

        if (entity instanceof HasTenantId tenantEntity) {
            UUID currentTenantId = requestContext.getTenantId();

            // Tự động set TenantId nếu chưa có
            if (UuidHelper.isEmpty(tenantEntity.getTenantId())
                    && UuidHelper.isNotEmpty(currentTenantId)) {
                tenantEntity.setTenantId(currentTenantId);
                updatePropertyValue(event.getPersister(), event.getState(), "tenantId", currentTenantId);

                log.debug("Set TenantId for entity: {} - TenantId: {}",
                        entity.getClass().getSimpleName(), currentTenantId);
            }

            // Validate TenantId
            validateTenantAccess(tenantEntity.getTenantId(), currentTenantId, "INSERT");
        }

        return false;
    }

    @Override
    public boolean onPreUpdate(PreUpdateEvent event) {
        Object entity = event.getEntity();

        if (entity instanceof HasTenantId tenantEntity) {
            UUID currentTenantId = requestContext.getTenantId();

            // Validate TenantId - không cho phép update entity của tenant khác
            validateTenantAccess(tenantEntity.getTenantId(), currentTenantId, "UPDATE");
        }

        return false;
    }

    @Override
    public boolean onPreDelete(PreDeleteEvent event) {
        Object entity = event.getEntity();

        if (entity instanceof HasTenantId tenantEntity) {
            UUID currentTenantId = requestContext.getTenantId();

            // Validate TenantId - không cho phép delete entity của tenant khác
            validateTenantAccess(tenantEntity.getTenantId(), currentTenantId, "DELETE");
        }

        return false;
    }

    /**
     * Validate quyền truy cập tenant.
     */
    private void validateTenantAccess(UUID entityTenantId, UUID currentTenantId, String operation) {
        // Cho phép super admin (currentTenantId = null) truy cập tất cả
        if (UuidHelper.isEmpty(currentTenantId)) {
            return;
        }

        // Entity không có TenantId (data shared) - cho phép truy cập
        if (UuidHelper.isEmpty(entityTenantId)) {
            return;
        }

        // Validate tenant match
        if (!currentTenantId.equals(entityTenantId)) {
            String errorMessage = String.format(
                    "Tenant access violation in %s operation. Current tenant: %s, Entity tenant: %s",
                    operation, currentTenantId, entityTenantId
            );

            log.warn(errorMessage);
            throw new SecurityException(errorMessage);
        }
    }

    /**
     * Cập nhật giá trị property trong Hibernate state array.
     */
    private void updatePropertyValue(EntityPersister persister, Object[] state, String propertyName, Object value) {
        String[] propertyNames = persister.getPropertyNames();
        for (int i = 0; i < propertyNames.length; i++) {
            if (propertyNames[i].equals(propertyName)) {
                state[i] = value;
                break;
            }
        }
    }
}
