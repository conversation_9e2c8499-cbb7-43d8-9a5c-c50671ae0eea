<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.5</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>vn.osp</groupId>
    <artifactId>common</artifactId>
    <version>1.0.0</version>
    <name>common</name>
    <description>Th<PERSON> viện dùng chung dành cho các dự án Java Spring Boot</description>
    <url/>
    <licenses>
        <license/>
    </licenses>
    <developers>
        <developer/>
    </developers>
    <scm>
        <connection/>
        <developerConnection/>
        <tag/>
        <url/>
    </scm>
    <properties>
        <java.version>21</java.version>
        <opentelemetry.version>1.49.0</opentelemetry.version>
        <micrometer.version>1.15.3</micrometer.version>
        <micrometer-tracing.version>1.5.3</micrometer-tracing.version>
        <opentelemetry-instrumentation.version>2.19.0-alpha</opentelemetry-instrumentation.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <!-- OpenTelemetry BOM (quản lý version cho các artifact stable như exporter, sdk, etc.) -->
            <dependency>
                <groupId>io.opentelemetry</groupId>
                <artifactId>opentelemetry-bom</artifactId>
                <version>${opentelemetry.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- Force OpenTelemetry API Incubator to match SDK version -->
            <dependency>
                <groupId>io.opentelemetry</groupId>
                <artifactId>opentelemetry-api-incubator</artifactId>
                <version>${opentelemetry.version}-alpha</version>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-bom</artifactId>
                <version>${micrometer.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-tracing-bom</artifactId>
                <version>${micrometer-tracing.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <!-- Spring core -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <!-- Loại bỏ Logback -->
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Spring Web (HttpStatus, ProblemDetail, RestControllerAdvice, ExceptionHandler) -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- JPA API (OptimisticLockException) -->
        <dependency>
            <groupId>jakarta.persistence</groupId>
            <artifactId>jakarta.persistence-api</artifactId>
        </dependency>

        <!-- Spring DAO Exception (DataIntegrityViolationException) -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>

        <!-- Hibernate Core for Event Listeners and Interceptors -->
        <dependency>
            <groupId>org.hibernate.orm</groupId>
            <artifactId>hibernate-core</artifactId>
        </dependency>

        <!-- Spring Data JPA (optional, for repository support) -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>


        <!-- Validation (cho @Valid, @NotNull, GlobalExceptionHandler) -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- Log4j2 -->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>2.24.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>2.24.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j2-impl</artifactId>
            <version>2.24.3</version>
        </dependency>

        <!-- Nếu có thư viện Java dùng JUL (java.util.logging) thì redirect về SLF4J -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jul-to-slf4j</artifactId>
            <version>2.0.17</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.38</version>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- Micrometer Tracing (kết hợp với OTel) -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-tracing-bridge-otel</artifactId>
        </dependency>

        <!-- OpenTelemetry Exporter (gửi dữ liệu ra Collector, ví dụ SigNoz, Jaeger, Tempo) -->
        <dependency>
            <groupId>io.opentelemetry</groupId>
            <artifactId>opentelemetry-exporter-otlp</artifactId>
        </dependency>

        <!-- Publish metric-->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-otlp</artifactId>
        </dependency>

        <dependency>
            <groupId>io.opentelemetry.instrumentation</groupId>
            <artifactId>opentelemetry-log4j-appender-2.17</artifactId>
            <version>${opentelemetry-instrumentation.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-crypto</artifactId>
        </dependency>

        <!-- Bouncy Castle for SCrypt and Argon2 -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk18on</artifactId>
            <version>1.81</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.18.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>1.14.0</version>
        </dependency>

        <dependency>
            <groupId>org.msgpack</groupId>
            <artifactId>msgpack-core</artifactId>
            <version>0.9.10</version>
        </dependency>

        <dependency>
            <groupId>org.msgpack</groupId>
            <artifactId>jackson-dataformat-msgpack</artifactId>
            <version>0.9.10</version>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.21.2</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>org.springframework.boot</groupId>-->
        <!--            <artifactId>spring-boot-starter-security</artifactId>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>io.jsonwebtoken</groupId>-->
        <!--            <artifactId>jjwt-api</artifactId>-->
        <!--            <version>0.11.5</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>io.jsonwebtoken</groupId>-->
        <!--            <artifactId>jjwt-impl</artifactId>-->
        <!--            <version>0.11.5</version>-->
        <!--            <scope>runtime</scope>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>io.jsonwebtoken</groupId>-->
        <!--            <artifactId>jjwt-jackson</artifactId>-->
        <!--            <version>0.11.5</version>-->
        <!--            <scope>runtime</scope>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>org.mockito</groupId>-->
        <!--            <artifactId>mockito-inline</artifactId>-->
        <!--            <version>5.2.0</version>-->
        <!--            <scope>test</scope>-->
        <!--        </dependency>-->

        <!--        &lt;!&ndash; JUnit 5 Jupiter API &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>org.junit.jupiter</groupId>-->
        <!--            <artifactId>junit-jupiter-api</artifactId>-->
        <!--            <version>5.13.4</version> &lt;!&ndash; hoặc phiên bản mới nhất &ndash;&gt;-->
        <!--            <scope>test</scope>-->
        <!--        </dependency>-->

        <!--        &lt;!&ndash; JUnit Jupiter Engine (chạy test) &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>org.junit.jupiter</groupId>-->
        <!--            <artifactId>junit-jupiter-engine</artifactId>-->
        <!--            <version>5.13.4</version>-->
        <!--            <scope>test</scope>-->
        <!--        </dependency>-->


        <!-- H2 Database for testing -->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- Compiler plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.14.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <proc>full</proc>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.38</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>

            <!-- Spring Boot plugin: disable repackage vì đây là library -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
