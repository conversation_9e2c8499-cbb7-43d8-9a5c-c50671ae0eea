package vn.osp.common.infrastructure.listeners;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.boot.Metadata;
import org.hibernate.boot.spi.BootstrapContext;
import org.hibernate.engine.spi.SessionFactoryImplementor;
import org.hibernate.event.service.spi.EventListenerRegistry;
import org.hibernate.event.spi.EventType;
import org.hibernate.integrator.spi.Integrator;
import org.hibernate.service.spi.SessionFactoryServiceRegistry;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class HibernateListenerIntegratorConfiguration implements Integrator {
    private final AuditingEventListener auditingEventListener;
    private final SoftDeleteEventListener softDeleteEventListener;
    private final MultiTenantEventListener multiTenantEventListener;

    @Override
    public void integrate(
            Metadata metadata,
            BootstrapContext bootstrapContext,
            SessionFactoryImplementor sessionFactory) {

        // As you might expect, an EventListenerRegistry is the thing with which event
        // listeners are registered
        // It is a service so we look it up using the service registry
        final EventListenerRegistry eventListenerRegistry =
                bootstrapContext.getServiceRegistry().getService(EventListenerRegistry.class);

        // 3) This form adds the specified listener(s) to the end of the listener chain
        // Đăng ký Auditing Event Listeners
        eventListenerRegistry.getEventListenerGroup(EventType.PRE_INSERT).appendListener(auditingEventListener);
        eventListenerRegistry.getEventListenerGroup(EventType.PRE_UPDATE).appendListener(auditingEventListener);

        // Đăng ký Soft Delete Event Listener
        eventListenerRegistry.getEventListenerGroup(EventType.PRE_DELETE).appendListener(softDeleteEventListener);

        // Đăng ký Multi-Tenant Event Listeners
        eventListenerRegistry.getEventListenerGroup(EventType.PRE_INSERT).appendListener(multiTenantEventListener);
        eventListenerRegistry.getEventListenerGroup(EventType.PRE_UPDATE).appendListener(multiTenantEventListener);
        eventListenerRegistry.getEventListenerGroup(EventType.PRE_DELETE).appendListener(multiTenantEventListener);

    }

    @Override
    public void disintegrate(
            SessionFactoryImplementor sessionFactory,
            SessionFactoryServiceRegistry serviceRegistry) {

    }
}

