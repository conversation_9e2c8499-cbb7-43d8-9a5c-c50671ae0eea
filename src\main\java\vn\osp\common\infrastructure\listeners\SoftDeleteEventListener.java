package vn.osp.common.infrastructure.listeners;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.event.spi.PreDeleteEvent;
import org.hibernate.event.spi.PreDeleteEventListener;
import org.hibernate.persister.entity.EntityPersister;
import org.springframework.stereotype.Component;
import vn.osp.common.application.services.RequestContext;
import vn.osp.common.domain.domainentitytypes.HasModifiedByUuid;
import vn.osp.common.domain.domainentitytypes.SoftDelete;
import vn.osp.common.domain.domainentitytypes.SoftDeleteUuid;
import vn.osp.common.domain.helpers.UuidHelper;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Hibernate Event Listener thực hiện soft delete thay vì hard delete
 * cho các entity implement SoftDelete interface.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SoftDeleteEventListener implements PreDeleteEventListener {

    private final RequestContext requestContext;

    @Override
    public boolean onPreDelete(PreDeleteEvent event) {
        Object entity = event.getEntity();

        if (entity instanceof SoftDelete<?>) {
            performSoftDelete((SoftDelete<?>) entity, event);
            return true; // Chặn hard delete
        }

        return false; // Cho phép hard delete bình thường
    }

    private void performSoftDelete(SoftDelete<?> entity, PreDeleteEvent event) {
        LocalDateTime now = LocalDateTime.now();
        UUID currentUserId = requestContext.getUserId();

        // Set soft delete fields
        entity.setDeleted(true);
        entity.setDeletedAt(now);

        if (UuidHelper.isNotEmpty(currentUserId) && entity instanceof SoftDeleteUuid deletable) {
            deletable.setDeletedBy(currentUserId);
        }

        // Cập nhật giá trị trong Hibernate event
        updatePropertyValue(event.getPersister(), event.getDeletedState(), "isDeleted", true);
        updatePropertyValue(event.getPersister(), event.getDeletedState(), "deleted", now);
        if (UuidHelper.isNotEmpty(currentUserId) && entity instanceof HasModifiedByUuid) {
            updatePropertyValue(event.getPersister(), event.getDeletedState(), "deletedBy", currentUserId);
        }

        log.debug("Performed soft delete for entity: {} - DeletedAt: {}, DeletedBy: {}",
                entity.getClass().getSimpleName(), now, currentUserId);

//        // Thực hiện UPDATE thay vì DELETE
//        event.getSession().merge(entity);
//        event.getSession().flush();
//        event.getSession().update(entity);
    }

    /**
     * Cập nhật giá trị property trong Hibernate state array.
     */
    private void updatePropertyValue(EntityPersister persister, Object[] state, String propertyName, Object value) {
        String[] propertyNames = persister.getPropertyNames();
        for (int i = 0; i < propertyNames.length; i++) {
            if (propertyNames[i].equals(propertyName)) {
                state[i] = value;
                break;
            }
        }
    }
}
