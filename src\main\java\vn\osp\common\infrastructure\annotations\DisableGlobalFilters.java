package vn.osp.common.infrastructure.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation để explicitly disable global filters cho một method.
 * Sử dụng khi cần truy cập tất cả data bao gồm cả deleted records, 
 * records của tenant khác, hoặc disabled records.
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DisableGlobalFilters {
    
    /**
     * <PERSON>h sách filters cần disable. <PERSON>ếu empty thì disable tất cả.
     */
    String[] filters() default {};
    
    /**
     * <PERSON><PERSON> tả lý do disable filters.
     */
    String reason() default "";
    
    /**
     * C<PERSON> yêu cầu permission đặc biệt không.
     */
    boolean requiresSpecialPermission() default true;
}
