# Global Filters - Hướng dẫn Sử dụng

## Tổng quan

Global Filters là hệ thống tự động lọc dữ liệu theo soft delete, multi-tenant và enabled status mà không cần annotate từng entity. Hệ thống này đảm bảo data isolation và security một cách tự động.

## Các loại Filter

### 1. Soft Delete Filter (`softDeleteFilter`)
- **M<PERSON>c đích**: Chỉ hiển thị records chưa bị xóa mềm
- **Điều kiện**: `isDeleted = false OR isDeleted IS NULL`
- **Áp dụng cho**: Entities implement `SoftDelete` interface

### 2. Tenant Filter (`tenantFilter`)
- **Mục đích**: Chỉ hiển thị records của tenant hiện tại
- **Điều kiện**: `tenantId = :tenantId OR tenantId IS NULL`
- **Áp dụng cho**: Entities implement `HasTenantId` interface

### 3. Enabled Filter (`enabledFilter`)
- **<PERSON><PERSON><PERSON> đích**: Chỉ hiển thị records đang được kích hoạt
- **Điều kiện**: `enabled = true`
- **Áp dụng cho**: Entities implement `HasEnabled` interface

## Cách thức hoạt động

### Tự động áp dụng
Filters được tự động áp dụng thông qua:

1. **GlobalFilterInterceptor**: Tự động enable filters khi tạo session mới
2. **GlobalFilterAspect**: Tự động enable filters cho repository và service methods
3. **BaseFilteredRepository**: Tự động enable filters cho repository operations
4. **BaseFilteredService**: Cung cấp utilities để làm việc với filters

### Không cần annotation
Khác với cách tiếp cận truyền thống, bạn **KHÔNG CẦN** annotate entities với `@Filter`:

```java
// ❌ KHÔNG CẦN làm như này
@Entity
@Filter(name = "softDeleteFilter")
@Filter(name = "tenantFilter")
public class MyEntity { ... }

// ✅ CHỈ CẦN implement interfaces
@Entity
public class MyEntity implements SoftDeleteUuid, HasTenantId, HasEnabled {
    // Filters sẽ được áp dụng tự động
}
```

## Cách sử dụng

### 1. Entity Design

```java
@Entity
@Table(name = "my_entities")
public class MyEntity extends BaseDomainEntityUuid 
        implements SoftDeleteUuid, HasTenantId, HasEnabled {
    
    @Column(name = "name")
    private String name;
    
    // Implement required fields từ interfaces
    @Column(name = "is_deleted")
    private boolean isDeleted = false;
    
    @Column(name = "tenant_id")
    private UUID tenantId;
    
    @Column(name = "enabled")
    private boolean enabled = true;
    
    // ... other fields and methods
}
```

### 2. Repository Usage

#### Option A: Extend BaseFilteredRepository
```java
@Repository
public class MyRepositoryImpl extends BaseFilteredRepository<MyEntity, UUID> {
    
    public MyRepositoryImpl(EntityManager entityManager) {
        super(MyEntity.class, entityManager);
    }
    
    // Tất cả methods sẽ tự động có filters
    public List<MyEntity> findActiveRecords() {
        return findAll(); // Tự động áp dụng filters
    }
    
    public List<MyEntity> findAllIncludingDeleted() {
        return findAllWithoutFilters(); // Bỏ qua tất cả filters
    }
}
```

#### Option B: Sử dụng JpaRepository với annotations
```java
@Repository
public interface MyRepository extends JpaRepository<MyEntity, UUID> {
    
    // Tự động áp dụng filters
    List<MyEntity> findByName(String name);
    
    // Explicitly enable filters
    @EnableGlobalFilters(reason = "Ensure data security")
    @Query("SELECT e FROM MyEntity e WHERE e.name = :name")
    List<MyEntity> findByNameWithFilters(@Param("name") String name);
    
    // Disable specific filters
    @DisableGlobalFilters(
        filters = {"softDeleteFilter"}, 
        reason = "Administrative function"
    )
    @Query("SELECT e FROM MyEntity e WHERE e.name = :name")
    List<MyEntity> findByNameIncludingDeleted(@Param("name") String name);
}
```

### 3. Service Usage

```java
@Service
public class MyService extends BaseFilteredService {
    
    @Autowired
    private MyRepository repository;
    
    // Tự động áp dụng filters
    @Transactional(readOnly = true)
    public List<MyEntity> getAllActive() {
        return repository.findAll();
    }
    
    // Sử dụng utility methods
    @Transactional(readOnly = true)
    public List<MyEntity> getAllIncludingDeleted() {
        return executeWithoutFilters(() -> repository.findAll());
    }
    
    @Transactional(readOnly = true)
    public List<MyEntity> getOnlyTenantFiltered() {
        return executeWithTenantFilterOnly(() -> repository.findAll());
    }
}
```

## Annotations

### @EnableGlobalFilters
Explicitly enable global filters cho một method:

```java
@EnableGlobalFilters(reason = "Ensure data security for sensitive operation")
public List<MyEntity> getSensitiveData() {
    // Filters sẽ được enable
}
```

### @DisableGlobalFilters
Disable global filters cho một method (cần cẩn thận):

```java
@DisableGlobalFilters(
    reason = "Administrative report requiring all data",
    requiresSpecialPermission = true
)
public List<MyEntity> getAdminReport() {
    // Tất cả filters sẽ được disable
}

@DisableGlobalFilters(
    filters = {"softDeleteFilter"},
    reason = "Recovery operation for deleted records"
)
public List<MyEntity> getDeletedRecords() {
    // Chỉ soft delete filter được disable
}
```

## Best Practices

### 1. Entity Design
- Luôn implement các interfaces cần thiết: `SoftDeleteUuid`, `HasTenantId`, `HasEnabled`
- Sử dụng naming convention nhất quán cho database columns
- Đặt default values phù hợp (`isDeleted = false`, `enabled = true`)

### 2. Repository Design
- Extend `BaseFilteredRepository` cho custom repositories
- Sử dụng `@DisableGlobalFilters` một cách có trách nhiệm
- Luôn cung cấp `reason` rõ ràng khi disable filters

### 3. Service Design
- Extend `BaseFilteredService` để có access đến filter utilities
- Sử dụng `executeWithoutFilters()` cho administrative operations
- Implement permission checks trước khi disable filters

### 4. Security
- Luôn validate permissions trước khi disable filters
- Log tất cả operations disable filters cho audit
- Sử dụng `requiresSpecialPermission = true` cho sensitive operations

## Troubleshooting

### 1. Filters không hoạt động
```java
// Kiểm tra entity có implement đúng interfaces không
if (entity instanceof SoftDeleteUuid) {
    // Soft delete filter sẽ được áp dụng
}

// Manually enable filters nếu cần
filterHelper.enableGlobalFilters(entityManager);
```

### 2. Cần access deleted data
```java
// Sử dụng utility methods
List<MyEntity> allData = executeWithoutFilters(() -> repository.findAll());

// Hoặc sử dụng annotation
@DisableGlobalFilters(reason = "Recovery operation")
public List<MyEntity> getDeletedRecords() {
    return repository.findAll();
}
```

### 3. Performance issues
```java
// Disable filters không cần thiết cho specific operations
@DisableGlobalFilters(
    filters = {"enabledFilter"}, 
    reason = "Performance optimization for bulk operation"
)
public void bulkOperation() {
    // Chỉ disable enabled filter, giữ lại soft delete và tenant filters
}
```

## Configuration

### Application Properties
```yaml
# Hibernate configuration
spring:
  jpa:
    properties:
      hibernate:
        # Global filter interceptor sẽ được tự động đăng ký
        session_factory.interceptor: vn.osp.common.infrastructure.filters.GlobalFilterInterceptor
```

### Custom Configuration
```java
@Configuration
public class CustomFilterConfiguration {
    
    @Bean
    public GlobalFilterManager customFilterManager() {
        // Custom filter manager nếu cần
        return new GlobalFilterManager();
    }
}
```

## Monitoring và Logging

Hệ thống tự động log các hoạt động filter:

```
DEBUG - Enabled global filters for session
DEBUG - Enabled soft delete filter for EntityManager  
DEBUG - Disabled global filters for administrative operation
WARN  - Failed to enable global filters: reason
```

Để enable debug logging:

```yaml
logging:
  level:
    vn.osp.common.infrastructure.filters: DEBUG
    vn.osp.common.infrastructure.aspects: DEBUG
```
