spring:
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        '[format_sql]': true
        '[show_sql]': true
    show-sql: true
  
  h2:
    console:
      enabled: true

logging:
  level:
    '[org.hibernate.SQL]': DEBUG
    '[org.hibernate.type.descriptor.sql.BasicBinder]': TRACE
    '[vn.osp.common]': DEBUG
