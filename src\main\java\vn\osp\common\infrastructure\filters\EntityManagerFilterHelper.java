package vn.osp.common.infrastructure.filters;

import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.Session;
import org.springframework.stereotype.Component;

import java.util.function.Supplier;

/**
 * Helper class để enable/disable global filters cho EntityManager.
 * Cung cấp API đơn giản để sử dụng trong service layer.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class EntityManagerFilterHelper {

    private final GlobalFilterManager filterManager;

    /**
     * Enable tất cả global filters cho EntityManager.
     */
    public void enableGlobalFilters(EntityManager entityManager) {
        try {
            Session session = entityManager.unwrap(Session.class);
            filterManager.enableGlobalFilters(session);
            log.debug("Enabled global filters for EntityManager");
        } catch (Exception e) {
            log.warn("Failed to enable global filters: {}", e.getMessage());
        }
    }

    /**
     * Disable tất cả global filters cho EntityManager.
     */
    public void disableGlobalFilters(EntityManager entityManager) {
        try {
            Session session = entityManager.unwrap(Session.class);
            filterManager.disableGlobalFilters(session);
            log.debug("Disabled global filters for EntityManager");
        } catch (Exception e) {
            log.warn("Failed to disable global filters: {}", e.getMessage());
        }
    }

    /**
     * Enable soft delete filter cho EntityManager.
     */
    public void enableSoftDeleteFilter(EntityManager entityManager) {
        try {
            Session session = entityManager.unwrap(Session.class);
            filterManager.enableSoftDeleteFilter(session);
            log.debug("Enabled soft delete filter for EntityManager");
        } catch (Exception e) {
            log.warn("Failed to enable soft delete filter: {}", e.getMessage());
        }
    }

    /**
     * Enable tenant filter cho EntityManager.
     */
    public void enableTenantFilter(EntityManager entityManager) {
        try {
            Session session = entityManager.unwrap(Session.class);
            filterManager.enableTenantFilter(session);
            log.debug("Enabled tenant filter for EntityManager");
        } catch (Exception e) {
            log.warn("Failed to enable tenant filter: {}", e.getMessage());
        }
    }

    /**
     * Enable enabled filter cho EntityManager.
     */
    public void enableEnabledFilter(EntityManager entityManager) {
        try {
            Session session = entityManager.unwrap(Session.class);
            filterManager.enableEnabledFilter(session);
            log.debug("Enabled 'enabled' filter for EntityManager");
        } catch (Exception e) {
            log.warn("Failed to enable 'enabled' filter: {}", e.getMessage());
        }
    }

    /**
     * Thực thi một block code với tất cả filters được disable.
     */
    public <T> T executeWithoutFilters(EntityManager entityManager, Supplier<T> supplier) {
        try {
            Session session = entityManager.unwrap(Session.class);
            return filterManager.executeWithoutFilters(session, supplier);
        } catch (Exception e) {
            log.warn("Failed to execute without filters, proceeding normally: {}", e.getMessage());
            return supplier.get();
        }
    }

    /**
     * Thực thi một block code với tất cả filters được disable (void return).
     */
    public void executeWithoutFilters(EntityManager entityManager, Runnable runnable) {
        executeWithoutFilters(entityManager, () -> {
            runnable.run();
            return null;
        });
    }

    /**
     * Thực thi một block code với filter được disable.
     */
    public <T> T executeWithoutFilter(EntityManager entityManager, String filterName, Supplier<T> supplier) {
        try {
            Session session = entityManager.unwrap(Session.class);
            return filterManager.executeWithoutFilter(session, filterName, supplier);
        } catch (Exception e) {
            log.warn("Failed to execute without filters, proceeding normally: {}", e.getMessage());
            return supplier.get();
        }
    }

    /**
     * Thực thi một block code với tất cả filters được disable (void return).
     */
    public void executeWithoutFilter(EntityManager entityManager, String filterName, Runnable runnable) {
        executeWithoutFilter(entityManager, filterName, () -> {
            runnable.run();
            return null;
        });
    }

    /**
     * Enable filters cho một entity class cụ thể.
     */
    public void enableFiltersForEntityClass(EntityManager entityManager, Class<?> entityClass) {
        try {
            Session session = entityManager.unwrap(Session.class);
            filterManager.enableFiltersForEntityClass(session, entityClass);
            log.debug("Enabled filters for entity class: {}", entityClass.getSimpleName());
        } catch (Exception e) {
            log.warn("Failed to enable filters for entity class {}: {}", entityClass.getSimpleName(), e.getMessage());
        }
    }
}
