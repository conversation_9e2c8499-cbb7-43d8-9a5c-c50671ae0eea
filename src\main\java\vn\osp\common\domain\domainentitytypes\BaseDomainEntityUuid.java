package vn.osp.common.domain.domainentitytypes;

import java.util.UUID;

/**
 * Class base, tất cả các model đều cần phải kế thừa từ class này
 * để tận dụng lại các field có sẵn, trong đó fix sẵn các key khóa ngoại là UUID.
 */
public abstract class BaseDomainEntityUuid extends BaseDomainEntity<UUID> {

    /**
     * Khởi tạo một đối tượng BaseDomainEntity mới với Id là UUID được generate tự động.
     */
    public BaseDomainEntityUuid() {
        super(null); // Để Hibernate tự generate UUID
    }
}
