package vn.osp.common.domain.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.util.UUID;

/**
 * BasicEntity - Entity đơn gi<PERSON><PERSON> không implement các marker interface
 * để test global filter chỉ áp dụng cho entities có đúng interface
 */
@Entity
@Table(name = "basic_entity")
public class BasicEntity {
    
    @Id
    @Column(name = "id")
    private UUID id;
    
    @Column(name = "name", nullable = false, length = 255)
    private String name;
    
    // Default constructor for JPA
    public BasicEntity() {
    }
    
    public BasicEntity(UUID id, String name) {
        this.id = id;
        this.name = name;
    }
    
    // Getters and setters
    public UUID getId() {
        return id;
    }
    
    public void setId(UUID id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    @Override
    public String toString() {
        return "BasicEntity{" +
                "id=" + id +
                ", name='" + name + '\'' +
                '}';
    }
}
